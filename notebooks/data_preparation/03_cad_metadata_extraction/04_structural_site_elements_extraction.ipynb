{"cells": [{"cell_type": "markdown", "metadata": {"tags": ["parameters"]}, "source": ["# Structural and Site Elements CAD Extraction\n", "\n", "Extract foundation piles, roads, fencing, and electrical infrastructure from structural/site CAD files.\n", "\n", "**Purpose**: Extract structural and site infrastructure for comprehensive validation  \n", "**Input**: Structural drawings, site plans, electrical drawings (DXF format)  \n", "**Output**: Foundation piles, roads, fencing, electrical infrastructure CSV files  \n", "\n", "**Note**: This notebook requires separate CAD files from solar equipment drawings.  \n", "Solar equipment extraction is handled in `02_solar_cad_extraction.ipynb`.\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Papermill parameters\n", "site_name = \"Castro\"\n", "project_type = \"ENEL\"\n", "\n", "# Structural/Site CAD files (separate from solar equipment files)\n", "structural_files = [\n", "    # \"foundation_plan.dxf\",\n", "    # \"site_plan.dxf\", \n", "    # \"electrical_plan.dxf\"\n", "]\n", "\n", "search_path = \"../../../data/raw\"\n", "output_dir = \"../../../output_runs/structural_extraction\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup and Configuration"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO: Structural/Site CAD extraction started\n", "INFO: Site: Castro, Project: ENEL\n", "INFO: Target files: []\n", "WARNING: No structural CAD files specified\n", "WARNING: This notebook requires separate structural/site drawings\n", "WARNING: Solar equipment extraction is handled in 02_solar_cad_extraction.ipynb\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "from pathlib import Path\n", "import ezdxf\n", "import json\n", "import logging\n", "from datetime import datetime\n", "from collections import defaultdict\n", "\n", "# Setup logging\n", "logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')\n", "logger = logging.getLogger(__name__)\n", "\n", "logger.info(\"Structural/Site CAD extraction started\")\n", "logger.info(f\"Site: {site_name}, Project: {project_type}\")\n", "logger.info(f\"Target files: {structural_files}\")\n", "\n", "if not structural_files:\n", "    logger.warning(\"No structural CAD files specified\")\n", "    logger.warning(\"This notebook requires separate structural/site drawings\")\n", "    logger.warning(\"Solar equipment extraction is handled in 02_solar_cad_extraction.ipynb\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load Coordinate System Configuration"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO: Loaded coordinate configuration: site_coordinate_config_20250729_134200.json\n", "WARNING: No coordinate validation ranges available\n"]}], "source": ["def load_coordinate_config(config_dir=\"../../../output_runs/coordinate_analysis\"):\n", "    \"\"\"Load coordinate system configuration for consistent validation.\"\"\"\n", "    config_path = Path(config_dir)\n", "    \n", "    if not config_path.exists():\n", "        logger.warning(f\"Configuration directory not found: {config_path}\")\n", "        return None\n", "    \n", "    config_files = list(config_path.glob(\"*/site_coordinate_config_*.json\"))\n", "    \n", "    if not config_files:\n", "        logger.warning(f\"No coordinate configuration found\")\n", "        return None\n", "    \n", "    latest_config = max(config_files, key=lambda x: x.stat().st_mtime)\n", "    \n", "    try:\n", "        with open(latest_config, 'r') as f:\n", "            config = json.load(f)\n", "        \n", "        logger.info(f\"Loaded coordinate configuration: {latest_config.name}\")\n", "        return config\n", "    \n", "    except Exception as e:\n", "        logger.error(f\"Error loading configuration: {e}\")\n", "        return None\n", "\n", "# Load configuration for consistent coordinate validation\n", "coord_config = load_coordinate_config()\n", "\n", "if coord_config and coord_config.get('valid_ranges'):\n", "    valid_ranges = coord_config['valid_ranges']\n", "    logger.info(f\"Using coordinate validation ranges from solar analysis\")\n", "else:\n", "    valid_ranges = None\n", "    logger.warning(\"No coordinate validation ranges available\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Structural Element Extraction Functions"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO: Structural extraction functions ready\n"]}], "source": ["def extract_foundation_piles(entity, layer, is_valid_coordinate):\n", "    \"\"\"Extract foundation piles from structural drawings.\"\"\"\n", "    piles = []\n", "    \n", "    # Look for foundation pile indicators\n", "    pile_keywords = ['PILE', 'FOUNDATION', 'BEAM', 'COLUMN', 'SUPPORT']\n", "    \n", "    if any(keyword in layer.upper() for keyword in pile_keywords):\n", "        \n", "        # Method 1: INSERT entities (pile symbols)\n", "        if entity.dxftype() == 'INSERT':\n", "            if hasattr(entity.dxf, 'insert'):\n", "                point = entity.dxf.insert\n", "                if is_valid_coordinate(point.x, point.y, point.z):\n", "                    piles.append({\n", "                        'x': point.x, 'y': point.y, 'z': point.z,\n", "                        'layer': layer,\n", "                        'block_name': getattr(entity.dxf, 'name', ''),\n", "                        'type': 'foundation_pile',\n", "                        'extraction_method': 'insert_symbol'\n", "                    })\n", "        \n", "        # Method 2: LINE entities (vertical lines representing I-beams)\n", "        elif entity.dxftype() == 'LINE':\n", "            if hasattr(entity.dxf, 'start') and hasattr(entity.dxf, 'end'):\n", "                start = entity.dxf.start\n", "                end = entity.dxf.end\n", "                \n", "                # Check if it's a vertical line (potential pile)\n", "                horizontal_distance = ((end.x - start.x)**2 + (end.y - start.y)**2)**0.5\n", "                vertical_distance = abs(end.z - start.z)\n", "                \n", "                if horizontal_distance < 0.5 and vertical_distance > 1.0:  # Vertical line\n", "                    if is_valid_coordinate(start.x, start.y, start.z):\n", "                        piles.append({\n", "                            'x': start.x, 'y': start.y, 'z': start.z,\n", "                            'layer': layer,\n", "                            'length': vertical_distance,\n", "                            'type': 'foundation_pile',\n", "                            'extraction_method': 'vertical_line'\n", "                        })\n", "        \n", "        # Method 3: CIRCLE entities (pile cross-sections)\n", "        elif entity.dxftype() == 'CIRCLE':\n", "            if hasattr(entity.dxf, 'center'):\n", "                center = entity.dxf.center\n", "                if is_valid_coordinate(center.x, center.y, center.z):\n", "                    piles.append({\n", "                        'x': center.x, 'y': center.y, 'z': center.z,\n", "                        'layer': layer,\n", "                        'radius': getattr(entity.dxf, 'radius', 0),\n", "                        'type': 'foundation_pile',\n", "                        'extraction_method': 'circle_symbol'\n", "                    })\n", "    \n", "    return piles\n", "\n", "def extract_site_infrastructure(entity, layer, is_valid_coordinate):\n", "    \"\"\"Extract roads, fencing, and other site infrastructure.\"\"\"\n", "    infrastructure = []\n", "    \n", "    # Road extraction\n", "    road_keywords = ['ROAD', 'PATH', 'ACCESS', 'DRIVE']\n", "    if any(keyword in layer.upper() for keyword in road_keywords):\n", "        if entity.dxftype() in ['LWPOLYLINE', 'POLYLINE', 'LINE']:\n", "            try:\n", "                if hasattr(entity, 'bbox'):\n", "                    bbox = entity.bbox()\n", "                    if bbox:\n", "                        center_x = (bbox[0].x + bbox[1].x) / 2\n", "                        center_y = (bbox[0].y + bbox[1].y) / 2\n", "                        \n", "                        if is_valid_coordinate(center_x, center_y):\n", "                            infrastructure.append({\n", "                                'x': center_x, 'y': center_y,\n", "                                'layer': layer,\n", "                                'type': 'road',\n", "                                'length': ((bbox[1].x - bbox[0].x)**2 + (bbox[1].y - bbox[0].y)**2)**0.5\n", "                            })\n", "            except:\n", "                pass\n", "    \n", "    # Fencing extraction\n", "    fence_keywords = ['FENCE', 'BOUNDARY', 'PERIMETER']\n", "    if any(keyword in layer.upper() for keyword in fence_keywords):\n", "        if entity.dxftype() in ['LWPOLYLINE', 'POLYLINE', 'LINE']:\n", "            try:\n", "                if hasattr(entity, 'bbox'):\n", "                    bbox = entity.bbox()\n", "                    if bbox:\n", "                        center_x = (bbox[0].x + bbox[1].x) / 2\n", "                        center_y = (bbox[0].y + bbox[1].y) / 2\n", "                        \n", "                        if is_valid_coordinate(center_x, center_y):\n", "                            infrastructure.append({\n", "                                'x': center_x, 'y': center_y,\n", "                                'layer': layer,\n", "                                'type': 'fencing',\n", "                                'length': ((bbox[1].x - bbox[0].x)**2 + (bbox[1].y - bbox[0].y)**2)**0.5\n", "                            })\n", "            except:\n", "                pass\n", "    \n", "    return infrastructure\n", "\n", "logger.info(\"Structural extraction functions ready\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## File Processing Status"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO: ============================================================\n", "INFO: STRUCTURAL/SITE CAD EXTRACTION STATUS\n", "INFO: ============================================================\n", "INFO: \n", "INFO: No structural CAD files specified for processing.\n", "INFO: \n", "INFO: Required CAD files for complete infrastructure extraction:\n", "INFO:   1. Foundation/Structural drawings (foundation_plan.dxf)\n", "INFO:   2. Site plan drawings (site_plan.dxf)\n", "INFO:   3. Electrical drawings (electrical_plan.dxf)\n", "INFO: \n", "INFO: Current status:\n", "INFO:   ✅ Solar equipment extraction: Available (02_solar_cad_extraction.ipynb)\n", "INFO:   ❌ Foundation piles: Requires structural drawings\n", "INFO:   ❌ Roads/Access: Requires site plan drawings\n", "INFO:   ❌ Fencing: Requires site plan drawings\n", "INFO:   ❌ Electrical: Requires electrical drawings\n", "INFO: \n", "INFO: Next steps:\n", "INFO:   1. Obtain separate structural/site CAD files\n", "INFO:   2. Update structural_files parameter with actual file names\n", "INFO:   3. Re-run this notebook for complete infrastructure extraction\n", "INFO: \n", "INFO: For now, proceed with solar equipment data for alignment workflows.\n", "INFO: ============================================================\n", "INFO: Status summary saved: ../../../output_runs/structural_extraction/castro_structural_extraction_20250729_160356/structural_extraction_status_20250729_160356.json\n"]}], "source": ["# Check if structural files are available\n", "if not structural_files:\n", "    logger.info(\"=\" * 60)\n", "    logger.info(\"STRUCTURAL/SITE CAD EXTRACTION STATUS\")\n", "    logger.info(\"=\" * 60)\n", "    logger.info(\"\")\n", "    logger.info(\"No structural CAD files specified for processing.\")\n", "    logger.info(\"\")\n", "    logger.info(\"Required CAD files for complete infrastructure extraction:\")\n", "    logger.info(\"  1. Foundation/Structural drawings (foundation_plan.dxf)\")\n", "    logger.info(\"  2. Site plan drawings (site_plan.dxf)\")\n", "    logger.info(\"  3. Electrical drawings (electrical_plan.dxf)\")\n", "    logger.info(\"\")\n", "    logger.info(\"Current status:\")\n", "    logger.info(\"  ✅ Solar equipment extraction: Available (02_solar_cad_extraction.ipynb)\")\n", "    logger.info(\"  ❌ Foundation piles: Requires structural drawings\")\n", "    logger.info(\"  ❌ Roads/Access: Requires site plan drawings\")\n", "    logger.info(\"  ❌ Fencing: Requires site plan drawings\")\n", "    logger.info(\"  ❌ Electrical: Requires electrical drawings\")\n", "    logger.info(\"\")\n", "    logger.info(\"Next steps:\")\n", "    logger.info(\"  1. Obtain separate structural/site CAD files\")\n", "    logger.info(\"  2. Update structural_files parameter with actual file names\")\n", "    logger.info(\"  3. Re-run this notebook for complete infrastructure extraction\")\n", "    logger.info(\"\")\n", "    logger.info(\"For now, proceed with solar equipment data for alignment workflows.\")\n", "    logger.info(\"=\" * 60)\n", "    \n", "    # Create placeholder summary\n", "    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "    output_path = Path(output_dir)\n", "    run_output_dir = output_path / f\"{site_name.lower()}_structural_extraction_{timestamp}\"\n", "    run_output_dir.mkdir(parents=True, exist_ok=True)\n", "    \n", "    placeholder_summary = {\n", "        'extraction_info': {\n", "            'site_name': site_name,\n", "            'timestamp': timestamp,\n", "            'status': 'awaiting_structural_cad_files'\n", "        },\n", "        'required_files': {\n", "            'foundation_drawings': 'foundation_plan.dxf',\n", "            'site_plan': 'site_plan.dxf',\n", "            'electrical_plan': 'electrical_plan.dxf'\n", "        },\n", "        'current_availability': {\n", "            'solar_equipment': 'Available via 02_solar_cad_extraction.ipynb',\n", "            'structural_elements': 'Awaiting CAD files'\n", "        },\n", "        'next_steps': [\n", "            'Obtain structural/site CAD files',\n", "            'Update notebook parameters',\n", "            'Re-run extraction',\n", "            'Proceed with alignment using available solar data'\n", "        ]\n", "    }\n", "    \n", "    summary_file = run_output_dir / f\"structural_extraction_status_{timestamp}.json\"\n", "    with open(summary_file, 'w') as f:\n", "        json.dump(placeholder_summary, f, indent=2)\n", "    \n", "    logger.info(f\"Status summary saved: {summary_file}\")\n", "    \n", "else:\n", "    logger.info(\"Structural CAD files specified - proceeding with extraction...\")\n", "    # Add actual processing logic here when files are available"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}