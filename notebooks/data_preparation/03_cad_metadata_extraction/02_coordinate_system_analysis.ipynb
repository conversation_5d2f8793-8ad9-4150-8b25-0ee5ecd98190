from pathlib import Path
import pandas as pd
import numpy as np
import ezdxf
import json

SITE = "nortan_res"
ROOT = Path("../../../data/raw") / SITE
DXF_DIR = ROOT / "converted_dxf"
CONFIG_OUT = ROOT / "site_coordinate_config.json"

print(f"[INFO] Site: {SITE}")
print(f"[INFO] Source DXF dir: {DXF_DIR}")
print(f"[INFO] Output config: {CONFIG_OUT}")


def extract_all_coords_from_dxf(file_path):
    doc = ezdxf.readfile(file_path)
    msp = doc.modelspace()
    coords = []

    for e in msp:
        if hasattr(e.dxf, "insert"):
            pt = e.dxf.insert
            coords.append({'x': pt.x, 'y': pt.y})
        elif e.dxftype() in ["LINE"]:
            coords.append({'x': e.dxf.start.x, 'y': e.dxf.start.y})
            coords.append({'x': e.dxf.end.x, 'y': e.dxf.end.y})
        elif e.dxftype() in ["LWPOLYLINE", "POLYLINE"]:
            for pt in e.get_points():
                coords.append({'x': pt[0], 'y': pt[1]})

    return pd.DataFrame(coords)

dxf_files = list(DXF_DIR.glob("*.dxf"))
assert dxf_files, "No DXF files found."
print(f"[INFO] Found {len(dxf_files)} DXF file(s)")

coords_df = extract_all_coords_from_dxf(dxf_files[0])
print(f"[INFO] Extracted {len(coords_df)} coordinates")


def detect_crs(xmin, ymin, xmax, ymax):
    x_range, y_range = xmax - xmin, ymax - ymin
    if 100_000 <= xmin <= 900_000 and 1_000_000 <= ymin <= 10_000_000:
        return {'type': 'UTM', 'confidence': 'high'}
    elif -180 <= xmin <= 180 and -90 <= ymin <= 90:
        return {'type': 'Geographic', 'confidence': 'high'}
    elif x_range < 100_000 and y_range < 100_000:
        return {'type': 'Local', 'confidence': 'medium'}
    return {'type': 'Unknown', 'confidence': 'low'}

xmin, xmax = coords_df['x'].min(), coords_df['x'].max()
ymin, ymax = coords_df['y'].min(), coords_df['y'].max()

crs_info = detect_crs(xmin, ymin, xmax, ymax)
print(f"[CRS] Detected: {crs_info}")

def iqr_bounds(arr):
    q1, q3 = np.percentile(arr, [25, 75])
    iqr = q3 - q1
    return q1 - 1.5 * iqr, q3 + 1.5 * iqr

x_lo, x_hi = iqr_bounds(coords_df['x'])
y_lo, y_hi = iqr_bounds(coords_df['y'])

print(f"[X] Valid range: {x_lo:.2f} – {x_hi:.2f}")
print(f"[Y] Valid range: {y_lo:.2f} – {y_hi:.2f}")

config = {
    'site': SITE,
    'crs': crs_info,
    'x_range': [x_lo, x_hi],
    'y_range': [y_lo, y_hi],
    'xmin': xmin,
    'ymin': ymin,
    'features': int(len(coords_df)),
    'type': crs_info['type'],
    'confidence': crs_info['confidence']
}

with open(CONFIG_OUT, "w") as f:
    json.dump(config, f, indent=2)

print(f"[INFO] Config written to {CONFIG_OUT}")