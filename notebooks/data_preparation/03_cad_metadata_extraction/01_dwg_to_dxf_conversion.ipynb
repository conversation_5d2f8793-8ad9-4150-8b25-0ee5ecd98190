import logging
import os
import subprocess
from pathlib import Path
from collections import defaultdict
from datetime import datetime
from typing import List, Dict, Optional, Tuple

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

print("DWG File Discovery and DXF Conversion - Starting...")
print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

# Define paths for DWG file discovery
project_root = Path('../../../data/raw/nortan_res/')  # Adjust to project root from notebooks/preprocessing/dwg_to_dxf/
data_path = project_root / 'cad'

print(f"Project root: {project_root.resolve()}")
print(f"Data path: {data_path.resolve()}")

# Verify the data directory exists
if not data_path.exists():
    logger.error(f"Data directory does not exist: {data_path}")
    raise FileNotFoundError(f"Directory not found: {data_path}")
elif not data_path.is_dir():
    logger.error(f"Data path is not a directory: {data_path}")
    raise NotADirectoryError(f"Path is not a directory: {data_path}")
else:
    logger.info(f"Data directory verified: {data_path}")

# Discover DWG files
dwg_files = list(data_path.rglob("*.dwg"))
conversion_status = defaultdict(dict)
subdir_count = defaultdict(int)

for dwg in dwg_files:
    if not os.access(dwg, os.R_OK):
        continue  # Skip unreadable files
    dxf = dwg.with_suffix(".dxf")
    rel_path = dwg.relative_to(data_path)
    subdir = rel_path.parts[0] if len(rel_path.parts) > 1 else "root"
    subdir_count[subdir] += 1
    conversion_status[str(dwg)] = {
        "dxf_exists": dxf.exists(),
        "needs_conversion": not dxf.exists(),
        "dwg_size_mb": round(dwg.stat().st_size / (1024 * 1024), 2),
        "dxf_size_mb": round(dxf.stat().st_size / (1024 * 1024), 2) if dxf.exists() else 0,
    }

print(f"\nDiscovered {len(dwg_files)} DWG files")
print("Conversion status per file:")
for path, status in conversion_status.items():
    print(f"- {path} | DXF: {'Yes' if status['dxf_exists'] else 'No'} | Size: {status['dwg_size_mb']}MB → {status['dxf_size_mb']}MB")

from pathlib import Path
import subprocess

# --- Check available conversion tools ---
def tool_available(cmd_or_path, test_arg="--version", allow_fail=False):
    try:
        result = subprocess.run([cmd_or_path, test_arg], capture_output=True, timeout=5)
        return result.returncode == 0 or allow_fail
    except Exception:
        return allow_fail  # allow_fail=True means we trust presence even if not executable

# ODA macOS app path
ODA_PATH = "/Applications/ODAFileConverter.app/Contents/MacOS/ODAFileConverter"
oda_installed = Path(ODA_PATH).exists()

# Trust ODA if present, even if CLI call hangs or fails
if oda_installed:
    try:
        subprocess.run([ODA_PATH], capture_output=True, timeout=5)
        oda_available = True
    except subprocess.TimeoutExpired:
        print(f"ODAFileConverter found at {ODA_PATH}, but execution timed out.")
        oda_available = True  # Still trust it
    except Exception as e:
        print(f"ODAFileConverter found at {ODA_PATH}, but could not run: {e}")
        oda_available = True  # Still trust it
else:
    oda_available = False

# Check other tools in PATH
tools = {
    "ODAFileConverter": oda_available,
    "dwg2dxf": tool_available("dwg2dxf"),
    "librecad": tool_available("librecad"),
    "qcad": tool_available("qcad"),
}

print("\nAvailable conversion tools:")
for name, available in tools.items():
    print(f"  {name}: {'Available' if available else 'Not Found'}")

if not any(tools.values()):
    print("\nNo conversion tools available. Please install ODAFileConverter or another DWG to DXF utility.")
else:
    print("\nAt least one DWG to DXF tool is available.")

print("\nDWG Check Completed.")
print("=" * 60)


import subprocess
import os

# ----- Configuration -----
TEIGHA_PATH = "/Applications/ODAFileConverter.app/Contents/MacOS/ODAFileConverter"  # For macOS GUI (launches UI)
# TEIGHA_PATH = "ODAFileConverter.exe"  # If using Wine or on Windows

INPUT_FOLDER = data_path
OUTPUT_FOLDER = f"{data_path.parent}/converted_dxf"
OUTVER = "ACAD2013"
OUTFORMAT = "DXF"
RECURSIVE = "0"
AUDIT = "1"
INPUTFILTER = "*.DWG"

print(f"Input folder: {INPUT_FOLDER}")
print(f"Output folder: {OUTPUT_FOLDER}")

# ----- Check if executable exists -----
if not os.path.exists(TEIGHA_PATH):
    raise FileNotFoundError(f"Converter not found at: {TEIGHA_PATH}")

# ----- Build command -----
cmd = [
    TEIGHA_PATH,
    INPUT_FOLDER,
    OUTPUT_FOLDER,
    OUTVER,
    OUTFORMAT,
    RECURSIVE,
    AUDIT,
    INPUTFILTER
]

# ----- Execute -----
try:
    result = subprocess.run(cmd, capture_output=True, text=True ,check=True)
    print("Conversion output:")
    print(result.stdout)
    # List converted files
    converted_files = list(Path(OUTPUT_FOLDER).rglob("*.dxf"))
    print(f"\nConverted {len(converted_files)} file(s):")
    for file in converted_files:
        print(f"- {file.name}")

except Exception as e:
    print(f"Error during conversion: {e}")
