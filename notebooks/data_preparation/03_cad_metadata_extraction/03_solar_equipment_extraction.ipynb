# Papermill parameters
site_name = "nortan_res"
project_type = "ENEL"
target_files = ["2024.08.26 - Norton_PILE-SP_All Piles_Preliminary.dxf"]
search_path = "../../../data/raw"
output_dir = "../../../preprocessed/nortan_res/cad_extraction"


import pandas as pd
import numpy as np
from pathlib import Path
import ezdxf
import json
import logging
from datetime import datetime
from collections import defaultdict

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

logger.info("Solar CAD extraction started")
logger.info(f"Site: {site_name}, Project: {project_type}")
logger.info(f"Target files: {target_files}")
logger.info(f"Search path: {search_path}")
logger.info(f"Output directory: {output_dir}")

# Coordinate system range config (already saved from coordinate system detection)
def load_coordinate_config(config_dir="../../../output_runs/coordinate_analysis"):
    config_path = Path(config_dir)
    config_files = list(config_path.glob("*/site_coordinate_config_*.json"))
    if not config_files:
        logger.warning("No config found")
        return None
    latest = max(config_files, key=lambda f: f.stat().st_mtime)
    return json.load(open(latest))

coord_config = load_coordinate_config()
valid_ranges = coord_config.get("valid_ranges") if coord_config else None

def create_coordinate_validator(valid_ranges):
    if not valid_ranges:
        # Use more permissive validation for large coordinate systems
        return lambda x, y, z=None: abs(x) > 1000 and abs(y) > 1000
    def validator(x, y, z=None):
        return (valid_ranges['x_min'] <= x <= valid_ranges['x_max'] and
                valid_ranges['y_min'] <= y <= valid_ranges['y_max'])
    return validator

is_valid_coordinate = create_coordinate_validator(valid_ranges)


import subprocess

def find_cad_files(search_path, target_files):
    found = []
    for name in target_files:
        result = subprocess.getoutput(f"find {search_path} -name '{name}'").strip()
        if result:
            found.append(Path(result))
    return found

cad_files = find_cad_files(search_path, target_files)
assert cad_files, "No CAD files found."
print(f"[INFO] Found {len(cad_files)} CAD file(s)")

from collections import Counter

layer_counts = Counter()
entity_types = Counter()
tracker_module_details = []

for f in cad_files:
    doc = ezdxf.readfile(f)
    msp = doc.modelspace()
    for e in msp:
        layer = getattr(e.dxf, 'layer', 'UNKNOWN')
        entity_type = e.dxftype()
        layer_counts[layer] += 1
        entity_types[entity_type] += 1
        
        # Check for tracker/module entities
        if 'TRACKER' in layer or 'ARRY' in layer:
            tracker_module_details.append({
                'layer': layer,
                'entity_type': entity_type,
                'block_name': getattr(e.dxf, 'name', '') if entity_type == 'INSERT' else 'N/A'
            })

print("\nTop layers in modelspace:")
for layer, count in layer_counts.most_common(20):
    print(f"{layer}: {count}")

print("\nEntity types found:")
for entity_type, count in entity_types.most_common(10):
    print(f"{entity_type}: {count}")

print("\nTracker/Module layer details:")
for detail in tracker_module_details[:10]:  # Show first 10
    print(f"Layer: {detail['layer']}, Type: {detail['entity_type']}, Block: {detail['block_name']}")


PILE_LAYERS = {
    "S-PILE_A_INT_BEAR", "S-PILE_B_INT_BEAR", "S-PILE_A_INT_SEISMIC", "S-PILE_B_INT_SEISMIC",
    "S-PILE_A_INT_GEAR", "S-PILE_B_INT_GEAR", "S-PILE_A_EXT_BEAR", "S-PILE_B_EXT_BEAR",
    "S-PILE_LBD", "S-PILE_INVERTER", "S-PILE_CAB-NS-INTER", "S-PILE_CAB_NS_TRANSITION",
    "S-PILE_CAB-NS", "S-PILE_CAB-EW", "S-PILE_A_EXT_SEISMIC", "S-PILE_B_EXT_SEISMIC"
}

# TRACKER_LAYERS = {"S-TRACKER_A", "S-TRACKER_B"}
# MODULE_LAYERS = {"E-ARRY-STR-24", "E-ARRY-STR-20"}

# Updated keywords based on actual layer analysis
tracker_keywords = ['TRACKER']
tracker_layers = {'S-TRACKER_A', 'S-TRACKER_B'}
module_keywords = ['ARRY', 'STR']
module_layers = {'E-ARRY-STR-24', 'E-ARRY-STR-20'}
pile_keywords = ['PILE', 'BEAR', 'SEISMIC', 'CAB', 'INVERTER', 'LBD']


def extract_solar_trackers(entity, layer):
    if layer in tracker_layers:
        try:
            if entity.dxftype() == 'INSERT':
                p = entity.dxf.insert
                cx, cy, cz = p.x, p.y, p.z
            elif entity.dxftype() in ['LWPOLYLINE', 'POLYLINE']:
                # Calculate centroid from vertices
                vertices = list(entity.vertices())
                if vertices:
                    x_coords = [v[0] for v in vertices]
                    y_coords = [v[1] for v in vertices]
                    z_coords = [v[2] if len(v) > 2 else 0.0 for v in vertices]
                    cx = sum(x_coords) / len(x_coords)
                    cy = sum(y_coords) / len(y_coords)
                    cz = sum(z_coords) / len(z_coords)
                else:
                    return []
            elif hasattr(entity.dxf, 'center'):
                p = entity.dxf.center
                cx, cy, cz = p.x, p.y, p.z
            else:
                return []
            
            if is_valid_coordinate(cx, cy, cz):
                return [{
                    'x': cx, 'y': cy, 'z': cz,
                    'layer': layer,
                    'block_name': getattr(entity.dxf, 'name', '') if entity.dxftype() == 'INSERT' else '',
                    'rotation': getattr(entity.dxf, 'rotation', 0.0),
                    'type': 'tracker'
                }]
        except:
            pass
    return []


def extract_solar_modules(entity, layer):
    if layer in module_layers:
        try:
            if entity.dxftype() in ['LWPOLYLINE', 'POLYLINE']:
                # Calculate centroid and dimensions from vertices
                vertices = list(entity.vertices())
                if vertices:
                    x_coords = [v[0] for v in vertices]
                    y_coords = [v[1] for v in vertices]
                    z_coords = [v[2] if len(v) > 2 else 0.0 for v in vertices]
                    cx = sum(x_coords) / len(x_coords)
                    cy = sum(y_coords) / len(y_coords)
                    cz = sum(z_coords) / len(z_coords)
                    width = max(x_coords) - min(x_coords)
                    height = max(y_coords) - min(y_coords)
                else:
                    return []
            elif entity.dxftype() == 'INSERT':
                p = entity.dxf.insert
                cx, cy, cz = p.x, p.y, p.z
                width = height = 1.0
            elif entity.dxftype() == 'CIRCLE':
                p = entity.dxf.center
                cx, cy, cz = p.x, p.y, p.z
                radius = entity.dxf.radius
                width = height = radius * 2
            else:
                return []
            
            if is_valid_coordinate(cx, cy, cz):
                return [{
                    'x': cx, 'y': cy, 'z': cz,
                    'layer': layer,
                    'width': width,
                    'height': height,
                    'block_name': getattr(entity.dxf, 'name', '') if entity.dxftype() == 'INSERT' else '',
                    'type': 'module'
                }]
        except:
            pass
    return []

def extract_pile_circles(entity, layer):
    layer_upper = layer.upper()
    if entity.dxftype() == 'CIRCLE' and any(k in layer_upper for k in pile_keywords):
        center = entity.dxf.center
        radius = entity.dxf.radius
        if is_valid_coordinate(center.x, center.y, center.z):
            # Try to find parent block reference (INSERT) if any
            parent_name = ''
            if hasattr(entity, 'block') and hasattr(entity.block, 'name'):
                parent_name = entity.block.name
            return [{
                'x': center.x, 'y': center.y, 'z': center.z,
                'radius': radius,
                'layer': layer,
                'block_name': parent_name,
                'type': 'pile'
            }]
    return []

from collections import Counter

entity_counter = Counter()

def process_cad_file(file_path):
    doc = ezdxf.readfile(file_path)
    msp = doc.modelspace()
    trackers = []
    modules = []
    piles = []
    
    for e in msp:
        layer = getattr(e.dxf, 'layer', 'UNKNOWN')
        trackers.extend(extract_solar_trackers(e, layer))
        modules.extend(extract_solar_modules(e, layer))
        piles.extend(extract_pile_circles(e, layer))
    
    return trackers, modules, piles

all_trackers = []
all_modules = []
all_piles = []

for f in cad_files:
    t, m, p = process_cad_file(f)
    for item in t + m + p:
        item['source_file'] = f.name
    all_trackers.extend(t)
    all_modules.extend(m)
    all_piles.extend(p)


timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
out_path = Path(output_dir)
out_path.mkdir(exist_ok=True, parents=True)

if all_trackers:
    pd.DataFrame(all_trackers).to_csv(out_path / f"{site_name.lower()}_trackers_{timestamp}.csv", index=False)
if all_modules:
    pd.DataFrame(all_modules).to_csv(out_path / f"{site_name.lower()}_modules_{timestamp}.csv", index=False)
if all_piles:
    pd.DataFrame(all_piles).to_csv(out_path / f"{site_name.lower()}_piles_{timestamp}.csv", index=False)

print(f"Extracted {len(all_trackers)} trackers, {len(all_modules)} modules, {len(all_piles)} piles.")


import geopandas as gpd
import pandas as pd
import numpy as np
from shapely.geometry import Point, Polygon
from pyproj import Transformer
import warnings

warnings.filterwarnings("ignore")  # Suppress geospatial and KML export warnings

# Transformer from CAD CRS (EPSG:3857) to WGS84 (EPSG:4326)
transformer = Transformer.from_crs("EPSG:3857", "EPSG:4326", always_xy=True)

# Define survey boundary in CAD projection (EPSG:3857)
survey_boundary_coords = [
    (2335367.6595, 10652597.6363),
    (2335624.5755, 10652597.5533),
    (2335624.5755, 10651698.6673),
    (2334666.9795, 10652112.1673),
    (2334666.9795, 10651698.5013)
]
survey_polygon = Polygon(survey_boundary_coords)

# Main data processing
if all_piles:
    # Load and clean
    piles_df = pd.DataFrame(all_piles)
    piles_df['z_numeric'] = pd.to_numeric(piles_df.get('z', 0.0), errors='coerce').fillna(0.0)

    # Survey inclusion check (still in CAD coords)
    piles_df['within_survey'] = piles_df.apply(
        lambda row: survey_polygon.contains(Point(row['x'], row['y'])), axis=1
    )

    # Summary stats
    total_piles = len(piles_df)
    piles_in_boundary = piles_df['within_survey'].sum()
    piles_outside_boundary = total_piles - piles_in_boundary

    print("Pile distribution within survey boundary:")
    print(piles_df.groupby(['layer', 'within_survey']).size().unstack(fill_value=0))
    print(f"\nSummary:")
    print(f"Total piles extracted: {total_piles}")
    print(f"Piles within survey boundary: {piles_in_boundary}")
    print(f"Piles outside survey boundary: {piles_outside_boundary}")
    print(f"Survey coverage: {piles_in_boundary / total_piles * 100:.1f}%")

    # Save filtered CSV
    surveyed_piles = piles_df[piles_df['within_survey']]
    if not surveyed_piles.empty:
        surveyed_piles.to_csv(out_path / f"{site_name.lower()}_piles_surveyed_{timestamp}.csv", index=False)
        print(f"\nSaved {len(surveyed_piles)} surveyed piles to CSV")

    # Coordinate transformation to WGS84
    transformed_coords = [transformer.transform(x, y) for x, y in zip(piles_df['x'], piles_df['y'])]
    piles_df['lon'], piles_df['lat'] = zip(*transformed_coords)
    piles_df['geometry'] = [Point(lon, lat) for lon, lat in transformed_coords]

    # Build GeoDataFrame with valid Points only
    gdf = gpd.GeoDataFrame(piles_df, geometry='geometry', crs="EPSG:4326")
    gdf = gdf[gdf.geometry.type == "Point"]
    gdf = gdf[gdf.is_valid]
    gdf['Name'] = gdf['layer'].astype(str)
    gdf['surveyed'] = piles_df['within_survey']

    # Export to KML
    out_file = out_path / f"{site_name.lower()}_cad_piles_{timestamp}.kml"
    gdf[['Name', 'geometry']].to_file(out_file, driver="KML")
    print(f"KML saved: {out_file}")

    # Export boundary polygon
    boundary_wgs = [transformer.transform(x, y) for x, y in survey_boundary_coords]
    boundary_wgs.append(boundary_wgs[0])  # Close the polygon
    boundary_polygon = Polygon(boundary_wgs)

    boundary_gdf = gpd.GeoDataFrame(
        [{"Name": "Surveyed Area", "geometry": boundary_polygon}],
        crs="EPSG:4326"
    )
    boundary_gdf = boundary_gdf[boundary_gdf.geometry.type == "Polygon"]
    boundary_gdf = boundary_gdf[boundary_gdf.is_valid]

    boundary_kml = out_path / f"{site_name.lower()}_survey_boundary_{timestamp}.kml"
    boundary_gdf.to_file(boundary_kml, driver="KML")
    print(f"Boundary KML saved: {boundary_kml}")

else:
    print("No pile data available for processing or KML export.")


# Step 1: Full CAD pile GeoDataFrame
full_gdf = gdf.copy()

# Step 2: Model comparison, validation, classification
# (run your PointNet++ analysis here using full_gdf)

# Step 3: Optional: split into chunks *just for KML export*
chunk_size = 2500
for i, start in enumerate(range(0, len(full_gdf), chunk_size)):
    chunk = full_gdf.iloc[start:start + chunk_size]
    chunk[['Name', 'geometry']].to_file(
        out_path / f"{site_name.lower()}_cad_piles_part{i+1}_{timestamp}.kml",
        driver="KML"
    )


import pandas as pd
import numpy as np
from pathlib import Path
from pyproj import Transformer

# CRS Definitions
POINTNET_CRS = "EPSG:32614"   # UTM Zone 14N (used by PointNet++)
CAD_CRS = "EPSG:32614"        # Same CRS for CAD and PointNet++ assumed

# No transformation needed if same CRS
transformer = Transformer.from_crs(POINTNET_CRS, CAD_CRS, always_xy=True)

# Path to PointNet++ results
pointnet_results_path = Path(
    "../../modeling/pile_detection/02_ml_based/pointnet_plus_plus_classification/output_runs/pointnet_plus_plus_inference/nortan_res"
)

# Load latest PointNet++ CSV
pointnet_csv_files = sorted(pointnet_results_path.glob("*detections*.csv")) if pointnet_results_path.exists() else []

if pointnet_csv_files and all_piles:
    pointnet_df = pd.read_csv(pointnet_csv_files[-1])
    cad_df = pd.DataFrame(all_piles)

    # Rename columns if needed (just in case)
    if 'x' not in pointnet_df.columns or 'y' not in pointnet_df.columns:
        raise ValueError("PointNet++ CSV must include 'x' and 'y' columns.")

    # OPTIONAL: Reproject if CRSs differ (disabled since both use EPSG:32614)
    # pointnet_df['x'], pointnet_df['y'] = transformer.transform(
    #     pointnet_df['x'].values, pointnet_df['y'].values
    # )

    print("SPATIAL EXTENT COMPARISON")
    for label, df in [("CAD", cad_df), ("PointNet++", pointnet_df)]:
        x_min, x_max = df['x'].min(), df['x'].max()
        y_min, y_max = df['y'].min(), df['y'].max()
        print(f"\n{label}:")
        print(f"  Count: {len(df)}")
        print(f"  X: {x_min:.2f} to {x_max:.2f} (Δ: {x_max - x_min:.2f} m)")
        print(f"  Y: {y_min:.2f} to {y_max:.2f} (Δ: {y_max - y_min:.2f} m)")

    # Overlap analysis
    x_overlap = max(0, min(cad_df['x'].max(), pointnet_df['x'].max()) - max(cad_df['x'].min(), pointnet_df['x'].min()))
    y_overlap = max(0, min(cad_df['y'].max(), pointnet_df['y'].max()) - max(cad_df['y'].min(), pointnet_df['y'].min()))
    cad_area = (cad_df['x'].max() - cad_df['x'].min()) * (cad_df['y'].max() - cad_df['y'].min())
    pointnet_area = (pointnet_df['x'].max() - pointnet_df['x'].min()) * (pointnet_df['y'].max() - pointnet_df['y'].min())
    overlap_area = x_overlap * y_overlap

    print("\nOVERLAP ANALYSIS")
    print(f"X overlap: {x_overlap:.2f} m")
    print(f"Y overlap: {y_overlap:.2f} m")
    print(f"Overlap area: {overlap_area:.2f} m²")
    print(f"CAD area: {cad_area:.2f} m²")
    print(f"PointNet++ area: {pointnet_area:.2f} m²")

    if overlap_area > 0:
        print(f"Overlap % (CAD): {overlap_area / cad_area * 100:.1f}%")
        print(f"Overlap % (PointNet++): {overlap_area / pointnet_area * 100:.1f}%")
    else:
        print("No spatial overlap detected.")

    # Center comparison
    cad_center = cad_df[['x', 'y']].mean()
    pointnet_center = pointnet_df[['x', 'y']].mean()
    center_dist = np.linalg.norm(cad_center - pointnet_center)

    print("\nCENTER ANALYSIS")
    print(f"CAD center: ({cad_center['x']:.2f}, {cad_center['y']:.2f})")
    print(f"PointNet++ center: ({pointnet_center['x']:.2f}, {pointnet_center['y']:.2f})")
    print(f"Center distance: {center_dist:.2f} m")

    print("\nRECOMMENDATION")
    if center_dist < 100:
        print("Likely same coordinate system. Use KML overlay to confirm.")
    elif center_dist < 1000:
        print("Possible offset. Inspect in QGIS/Google Earth.")
    else:
        print("Likely different coordinate systems. Verify projections.")
else:
    if not pointnet_csv_files:
        print(f"No PointNet++ CSV found in: {pointnet_results_path}")
    if not all_piles:
        print("Missing CAD pile data.")
