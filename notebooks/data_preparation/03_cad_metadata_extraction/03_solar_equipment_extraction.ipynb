# Papermill parameters
site_name = "nortan_res"
project_type = "ENEL"
target_files = ["2024.08.26 - Norton_PILE-SP_All Piles_Preliminary.dxf"]
search_path = "../../../data/raw"
output_dir = "../../../preprocessed/nortan_res/cad_extraction"


import pandas as pd
import numpy as np
from pathlib import Path
import ezdxf
import json
import logging
from datetime import datetime
from collections import defaultdict

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

logger.info("Solar CAD extraction started")
logger.info(f"Site: {site_name}, Project: {project_type}")
logger.info(f"Target files: {target_files}")
logger.info(f"Search path: {search_path}")
logger.info(f"Output directory: {output_dir}")

# Coordinate system range config (already saved from coordinate system detection)
def load_coordinate_config(config_dir="../../../output_runs/coordinate_analysis"):
    config_path = Path(config_dir)
    config_files = list(config_path.glob("*/site_coordinate_config_*.json"))
    if not config_files:
        logger.warning("No config found")
        return None
    latest = max(config_files, key=lambda f: f.stat().st_mtime)
    return json.load(open(latest))

coord_config = load_coordinate_config()
valid_ranges = coord_config.get("valid_ranges") if coord_config else None

def create_coordinate_validator(valid_ranges):
    if not valid_ranges:
        # Use more permissive validation for large coordinate systems
        return lambda x, y, z=None: abs(x) > 1000 and abs(y) > 1000
    def validator(x, y, z=None):
        return (valid_ranges['x_min'] <= x <= valid_ranges['x_max'] and
                valid_ranges['y_min'] <= y <= valid_ranges['y_max'])
    return validator

is_valid_coordinate = create_coordinate_validator(valid_ranges)


import subprocess

def find_cad_files(search_path, target_files):
    found = []
    for name in target_files:
        result = subprocess.getoutput(f"find {search_path} -name '{name}'").strip()
        if result:
            found.append(Path(result))
    return found

cad_files = find_cad_files(search_path, target_files)
assert cad_files, "No CAD files found."
print(f"[INFO] Found {len(cad_files)} CAD file(s)")

from collections import Counter

layer_counts = Counter()
entity_types = Counter()
tracker_module_details = []

for f in cad_files:
    doc = ezdxf.readfile(f)
    msp = doc.modelspace()
    for e in msp:
        layer = getattr(e.dxf, 'layer', 'UNKNOWN')
        entity_type = e.dxftype()
        layer_counts[layer] += 1
        entity_types[entity_type] += 1
        
        # Check for tracker/module entities
        if 'TRACKER' in layer or 'ARRY' in layer:
            tracker_module_details.append({
                'layer': layer,
                'entity_type': entity_type,
                'block_name': getattr(e.dxf, 'name', '') if entity_type == 'INSERT' else 'N/A'
            })

print("\nTop layers in modelspace:")
for layer, count in layer_counts.most_common(20):
    print(f"{layer}: {count}")

print("\nEntity types found:")
for entity_type, count in entity_types.most_common(10):
    print(f"{entity_type}: {count}")

print("\nTracker/Module layer details:")
for detail in tracker_module_details[:10]:  # Show first 10
    print(f"Layer: {detail['layer']}, Type: {detail['entity_type']}, Block: {detail['block_name']}")


PILE_LAYERS = {
    "S-PILE_A_INT_BEAR", "S-PILE_B_INT_BEAR", "S-PILE_A_INT_SEISMIC", "S-PILE_B_INT_SEISMIC",
    "S-PILE_A_INT_GEAR", "S-PILE_B_INT_GEAR", "S-PILE_A_EXT_BEAR", "S-PILE_B_EXT_BEAR",
    "S-PILE_LBD", "S-PILE_INVERTER", "S-PILE_CAB-NS-INTER", "S-PILE_CAB_NS_TRANSITION",
    "S-PILE_CAB-NS", "S-PILE_CAB-EW", "S-PILE_A_EXT_SEISMIC", "S-PILE_B_EXT_SEISMIC"
}

# TRACKER_LAYERS = {"S-TRACKER_A", "S-TRACKER_B"}
# MODULE_LAYERS = {"E-ARRY-STR-24", "E-ARRY-STR-20"}

# Updated keywords based on actual layer analysis
tracker_keywords = ['TRACKER']
tracker_layers = {'S-TRACKER_A', 'S-TRACKER_B'}
module_keywords = ['ARRY', 'STR']
module_layers = {'E-ARRY-STR-24', 'E-ARRY-STR-20'}
pile_keywords = ['PILE', 'BEAR', 'SEISMIC', 'CAB', 'INVERTER', 'LBD']


def extract_solar_trackers(entity, layer):
    if layer in tracker_layers:
        try:
            if entity.dxftype() == 'INSERT':
                p = entity.dxf.insert
                cx, cy, cz = p.x, p.y, p.z
            elif entity.dxftype() in ['LWPOLYLINE', 'POLYLINE']:
                # Calculate centroid from vertices
                vertices = list(entity.vertices())
                if vertices:
                    x_coords = [v[0] for v in vertices]
                    y_coords = [v[1] for v in vertices]
                    z_coords = [v[2] if len(v) > 2 else 0.0 for v in vertices]
                    cx = sum(x_coords) / len(x_coords)
                    cy = sum(y_coords) / len(y_coords)
                    cz = sum(z_coords) / len(z_coords)
                else:
                    return []
            elif hasattr(entity.dxf, 'center'):
                p = entity.dxf.center
                cx, cy, cz = p.x, p.y, p.z
            else:
                return []
            
            if is_valid_coordinate(cx, cy, cz):
                return [{
                    'x': cx, 'y': cy, 'z': cz,
                    'layer': layer,
                    'block_name': getattr(entity.dxf, 'name', '') if entity.dxftype() == 'INSERT' else '',
                    'rotation': getattr(entity.dxf, 'rotation', 0.0),
                    'type': 'tracker'
                }]
        except:
            pass
    return []


def extract_solar_modules(entity, layer):
    if layer in module_layers:
        try:
            if entity.dxftype() in ['LWPOLYLINE', 'POLYLINE']:
                # Calculate centroid and dimensions from vertices
                vertices = list(entity.vertices())
                if vertices:
                    x_coords = [v[0] for v in vertices]
                    y_coords = [v[1] for v in vertices]
                    z_coords = [v[2] if len(v) > 2 else 0.0 for v in vertices]
                    cx = sum(x_coords) / len(x_coords)
                    cy = sum(y_coords) / len(y_coords)
                    cz = sum(z_coords) / len(z_coords)
                    width = max(x_coords) - min(x_coords)
                    height = max(y_coords) - min(y_coords)
                else:
                    return []
            elif entity.dxftype() == 'INSERT':
                p = entity.dxf.insert
                cx, cy, cz = p.x, p.y, p.z
                width = height = 1.0
            elif entity.dxftype() == 'CIRCLE':
                p = entity.dxf.center
                cx, cy, cz = p.x, p.y, p.z
                radius = entity.dxf.radius
                width = height = radius * 2
            else:
                return []
            
            if is_valid_coordinate(cx, cy, cz):
                return [{
                    'x': cx, 'y': cy, 'z': cz,
                    'layer': layer,
                    'width': width,
                    'height': height,
                    'block_name': getattr(entity.dxf, 'name', '') if entity.dxftype() == 'INSERT' else '',
                    'type': 'module'
                }]
        except:
            pass
    return []

def extract_pile_circles(entity, layer):
    layer_upper = layer.upper()
    if entity.dxftype() == 'CIRCLE' and any(k in layer_upper for k in pile_keywords):
        center = entity.dxf.center
        radius = entity.dxf.radius
        if is_valid_coordinate(center.x, center.y, center.z):
            # Try to find parent block reference (INSERT) if any
            parent_name = ''
            if hasattr(entity, 'block') and hasattr(entity.block, 'name'):
                parent_name = entity.block.name
            return [{
                'x': center.x, 'y': center.y, 'z': center.z,
                'radius': radius,
                'layer': layer,
                'block_name': parent_name,
                'type': 'pile'
            }]
    return []

from collections import Counter

entity_counter = Counter()

def process_cad_file(file_path):
    doc = ezdxf.readfile(file_path)
    msp = doc.modelspace()
    trackers = []
    modules = []
    piles = []
    
    for e in msp:
        layer = getattr(e.dxf, 'layer', 'UNKNOWN')
        trackers.extend(extract_solar_trackers(e, layer))
        modules.extend(extract_solar_modules(e, layer))
        piles.extend(extract_pile_circles(e, layer))
    
    return trackers, modules, piles

all_trackers = []
all_modules = []
all_piles = []

for f in cad_files:
    t, m, p = process_cad_file(f)
    for item in t + m + p:
        item['source_file'] = f.name
    all_trackers.extend(t)
    all_modules.extend(m)
    all_piles.extend(p)


timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
out_path = Path(output_dir)
out_path.mkdir(exist_ok=True, parents=True)

if all_trackers:
    pd.DataFrame(all_trackers).to_csv(out_path / f"{site_name.lower()}_trackers_{timestamp}.csv", index=False)
if all_modules:
    pd.DataFrame(all_modules).to_csv(out_path / f"{site_name.lower()}_modules_{timestamp}.csv", index=False)
if all_piles:
    pd.DataFrame(all_piles).to_csv(out_path / f"{site_name.lower()}_piles_{timestamp}.csv", index=False)

print(f"Extracted {len(all_trackers)} trackers, {len(all_modules)} modules, {len(all_piles)} piles.")
