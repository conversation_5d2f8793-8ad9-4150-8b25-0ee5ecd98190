# Papermill parameters
site_name = "nortan_res"
project_type = "ENEL"
target_files = ["2024.08.26 - Norton_PILE-SP_All Piles_Preliminary.dxf"]
search_path = "../../../data/raw"
output_dir = "../../../preprocessed/nortan_res/cad_extraction"


import pandas as pd
import numpy as np
from pathlib import Path
import ezdxf
import json
import logging
from datetime import datetime
from collections import defaultdict

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

logger.info("Solar CAD extraction started")
logger.info(f"Site: {site_name}, Project: {project_type}")
logger.info(f"Target files: {target_files}")
logger.info(f"Search path: {search_path}")
logger.info(f"Output directory: {output_dir}")

# Coordinate system range config (already saved from coordinate system detection)
def load_coordinate_config(config_dir="../../../output_runs/coordinate_analysis"):
    config_path = Path(config_dir)
    config_files = list(config_path.glob("*/site_coordinate_config_*.json"))
    if not config_files:
        logger.warning("No config found")
        return None
    latest = max(config_files, key=lambda f: f.stat().st_mtime)
    return json.load(open(latest))

coord_config = load_coordinate_config()
valid_ranges = coord_config.get("valid_ranges") if coord_config else None

def create_coordinate_validator(valid_ranges):
    if not valid_ranges:
        # Use more permissive validation for large coordinate systems
        return lambda x, y, z=None: abs(x) > 1000 and abs(y) > 1000
    def validator(x, y, z=None):
        return (valid_ranges['x_min'] <= x <= valid_ranges['x_max'] and
                valid_ranges['y_min'] <= y <= valid_ranges['y_max'])
    return validator

is_valid_coordinate = create_coordinate_validator(valid_ranges)


import subprocess

def find_cad_files(search_path, target_files):
    found = []
    for name in target_files:
        result = subprocess.getoutput(f"find {search_path} -name '{name}'").strip()
        if result:
            found.append(Path(result))
    return found

cad_files = find_cad_files(search_path, target_files)
assert cad_files, "No CAD files found."
print(f"[INFO] Found {len(cad_files)} CAD file(s)")

from collections import Counter

layer_counts = Counter()
entity_types = Counter()
tracker_module_details = []

for f in cad_files:
    doc = ezdxf.readfile(f)
    msp = doc.modelspace()
    for e in msp:
        layer = getattr(e.dxf, 'layer', 'UNKNOWN')
        entity_type = e.dxftype()
        layer_counts[layer] += 1
        entity_types[entity_type] += 1
        
        # Check for tracker/module entities
        if 'TRACKER' in layer or 'ARRY' in layer:
            tracker_module_details.append({
                'layer': layer,
                'entity_type': entity_type,
                'block_name': getattr(e.dxf, 'name', '') if entity_type == 'INSERT' else 'N/A'
            })

print("\nTop layers in modelspace:")
for layer, count in layer_counts.most_common(20):
    print(f"{layer}: {count}")

print("\nEntity types found:")
for entity_type, count in entity_types.most_common(10):
    print(f"{entity_type}: {count}")

print("\nTracker/Module layer details:")
for detail in tracker_module_details[:10]:  # Show first 10
    print(f"Layer: {detail['layer']}, Type: {detail['entity_type']}, Block: {detail['block_name']}")


# SOLAR TRACKER FOUNDATION PILES ONLY - Exclude infrastructure piles
# Only include piles that support solar trackers (bearing, seismic, gear)
SOLAR_TRACKER_PILE_LAYERS = {
    "S-PILE_A_INT_BEAR", "S-PILE_B_INT_BEAR",      # Interior bearing piles
    "S-PILE_A_INT_SEISMIC", "S-PILE_B_INT_SEISMIC", # Interior seismic piles
    "S-PILE_A_INT_GEAR", "S-PILE_B_INT_GEAR",       # Interior gear piles
    "S-PILE_A_EXT_BEAR", "S-PILE_B_EXT_BEAR",       # Exterior bearing piles
    "S-PILE_A_EXT_SEISMIC", "S-PILE_B_EXT_SEISMIC"  # Exterior seismic piles
}

# EXCLUDED PILE TYPES (infrastructure, not solar tracker foundations):
EXCLUDED_PILE_LAYERS = {
    "S-PILE_INVERTER",           # Inverter equipment piles
    "S-PILE_CAB-NS-INTER",      # Cable route piles (North-South Interconnect)
    "S-PILE_CAB_NS_TRANSITION", # Cable route transition piles
    "S-PILE_CAB-NS",            # Cable route piles (North-South)
    "S-PILE_CAB-EW",            # Cable route piles (East-West)
    "S-PILE_LBD",               # Load break disconnect piles
    "S-PILE_MET",               # Meteorological station piles
    "S-PILE_SCADA"              # SCADA equipment piles
}

# Updated keywords for solar tracker foundations only
tracker_keywords = ['TRACKER']
tracker_layers = {'S-TRACKER_A', 'S-TRACKER_B'}
module_keywords = ['ARRY', 'STR']
module_layers = {'E-ARRY-STR-24', 'E-ARRY-STR-20'}

# Only include solar tracker foundation pile keywords
solar_pile_keywords = ['BEAR', 'SEISMIC', 'GEAR']  # Removed 'CAB', 'INVERTER', 'LBD'


def extract_solar_trackers(entity, layer):
    if layer in tracker_layers:
        try:
            if entity.dxftype() == 'INSERT':
                p = entity.dxf.insert
                cx, cy, cz = p.x, p.y, p.z
            elif entity.dxftype() in ['LWPOLYLINE', 'POLYLINE']:
                # Calculate centroid from vertices
                vertices = list(entity.vertices())
                if vertices:
                    x_coords = [v[0] for v in vertices]
                    y_coords = [v[1] for v in vertices]
                    z_coords = [v[2] if len(v) > 2 else 0.0 for v in vertices]
                    cx = sum(x_coords) / len(x_coords)
                    cy = sum(y_coords) / len(y_coords)
                    cz = sum(z_coords) / len(z_coords)
                else:
                    return []
            elif hasattr(entity.dxf, 'center'):
                p = entity.dxf.center
                cx, cy, cz = p.x, p.y, p.z
            else:
                return []
            
            if is_valid_coordinate(cx, cy, cz):
                return [{
                    'x': cx, 'y': cy, 'z': cz,
                    'layer': layer,
                    'block_name': getattr(entity.dxf, 'name', '') if entity.dxftype() == 'INSERT' else '',
                    'rotation': getattr(entity.dxf, 'rotation', 0.0),
                    'type': 'tracker'
                }]
        except:
            pass
    return []


def extract_solar_modules(entity, layer):
    if layer in module_layers:
        try:
            if entity.dxftype() in ['LWPOLYLINE', 'POLYLINE']:
                # Calculate centroid and dimensions from vertices
                vertices = list(entity.vertices())
                if vertices:
                    x_coords = [v[0] for v in vertices]
                    y_coords = [v[1] for v in vertices]
                    z_coords = [v[2] if len(v) > 2 else 0.0 for v in vertices]
                    cx = sum(x_coords) / len(x_coords)
                    cy = sum(y_coords) / len(y_coords)
                    cz = sum(z_coords) / len(z_coords)
                    width = max(x_coords) - min(x_coords)
                    height = max(y_coords) - min(y_coords)
                else:
                    return []
            elif entity.dxftype() == 'INSERT':
                p = entity.dxf.insert
                cx, cy, cz = p.x, p.y, p.z
                width = height = 1.0
            elif entity.dxftype() == 'CIRCLE':
                p = entity.dxf.center
                cx, cy, cz = p.x, p.y, p.z
                radius = entity.dxf.radius
                width = height = radius * 2
            else:
                return []
            
            if is_valid_coordinate(cx, cy, cz):
                return [{
                    'x': cx, 'y': cy, 'z': cz,
                    'layer': layer,
                    'width': width,
                    'height': height,
                    'block_name': getattr(entity.dxf, 'name', '') if entity.dxftype() == 'INSERT' else '',
                    'type': 'module'
                }]
        except:
            pass
    return []

def extract_solar_tracker_piles(entity, layer):
    """
    Extract ONLY solar tracker foundation piles, excluding infrastructure piles
    """
    # Only extract from explicitly allowed solar tracker pile layers
    if layer in SOLAR_TRACKER_PILE_LAYERS and entity.dxftype() == 'CIRCLE':
        center = entity.dxf.center
        radius = entity.dxf.radius
        if is_valid_coordinate(center.x, center.y, center.z):
            # Try to find parent block reference (INSERT) if any
            parent_name = ''
            if hasattr(entity, 'block') and hasattr(entity.block, 'name'):
                parent_name = entity.block.name
            
            # Determine pile function from layer name
            pile_function = 'unknown'
            if 'BEAR' in layer:
                pile_function = 'bearing'
            elif 'SEISMIC' in layer:
                pile_function = 'seismic'
            elif 'GEAR' in layer:
                pile_function = 'gear'
            
            # Determine pile location (interior vs exterior)
            pile_location = 'interior' if 'INT' in layer else 'exterior'
            
            # Determine tracker type (A or B)
            tracker_type = 'A' if '_A_' in layer else 'B' if '_B_' in layer else 'unknown'
            
            return [{
                'x': center.x, 'y': center.y, 'z': center.z,
                'radius': radius,
                'layer': layer,
                'block_name': parent_name,
                'type': 'solar_tracker_pile',
                'pile_function': pile_function,
                'pile_location': pile_location,
                'tracker_type': tracker_type
            }]
    
    # Log excluded pile types for verification
    elif layer in EXCLUDED_PILE_LAYERS and entity.dxftype() == 'CIRCLE':
        # Don't extract, but could log for debugging
        pass
    
    return []

from collections import Counter

entity_counter = Counter()

def process_cad_file(file_path):
    doc = ezdxf.readfile(file_path)
    msp = doc.modelspace()
    trackers = []
    modules = []
    solar_tracker_piles = []
    
    # Track excluded pile counts for reporting
    excluded_pile_counts = {layer: 0 for layer in EXCLUDED_PILE_LAYERS}
    
    for e in msp:
        layer = getattr(e.dxf, 'layer', 'UNKNOWN')
        trackers.extend(extract_solar_trackers(e, layer))
        modules.extend(extract_solar_modules(e, layer))
        solar_tracker_piles.extend(extract_solar_tracker_piles(e, layer))
        
        # Count excluded piles for reporting
        if layer in EXCLUDED_PILE_LAYERS and e.dxftype() == 'CIRCLE':
            excluded_pile_counts[layer] += 1
    
    # Report excluded pile counts
    total_excluded = sum(excluded_pile_counts.values())
    if total_excluded > 0:
        print(f"\nExcluded {total_excluded} infrastructure piles:")
        for layer, count in excluded_pile_counts.items():
            if count > 0:
                print(f"  {layer}: {count}")
    
    return trackers, modules, solar_tracker_piles

all_trackers = []
all_modules = []
all_solar_tracker_piles = []

for f in cad_files:
    t, m, p = process_cad_file(f)
    for item in t + m + p:
        item['source_file'] = f.name
    all_trackers.extend(t)
    all_modules.extend(m)
    all_solar_tracker_piles.extend(p)


timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
out_path = Path(output_dir)
out_path.mkdir(exist_ok=True, parents=True)

if all_trackers:
    pd.DataFrame(all_trackers).to_csv(out_path / f"{site_name.lower()}_trackers_{timestamp}.csv", index=False)
if all_modules:
    pd.DataFrame(all_modules).to_csv(out_path / f"{site_name.lower()}_modules_{timestamp}.csv", index=False)
if all_solar_tracker_piles:
    pd.DataFrame(all_solar_tracker_piles).to_csv(out_path / f"{site_name.lower()}_solar_tracker_piles_{timestamp}.csv", index=False)

print(f"Extracted {len(all_trackers)} trackers, {len(all_modules)} modules, {len(all_solar_tracker_piles)} solar tracker piles.")
print(f"\nNote: Excluded infrastructure piles (inverter, cable route, LBD, MET, SCADA) - see processing output above.")


if all_solar_tracker_piles:
    piles_df = pd.DataFrame(all_solar_tracker_piles)
    
    print("=== SOLAR TRACKER PILE EXTRACTION SUMMARY ===")
    print(f"\nExtracted Solar Tracker Piles by Layer:")
    layer_counts = piles_df['layer'].value_counts().sort_index()
    for layer, count in layer_counts.items():
        print(f"  {layer}: {count}")
    
    print(f"\nExtracted Solar Tracker Piles by Function:")
    function_counts = piles_df['pile_function'].value_counts()
    for function, count in function_counts.items():
        print(f"  {function}: {count}")
    
    print(f"\nExtracted Solar Tracker Piles by Location:")
    location_counts = piles_df['pile_location'].value_counts()
    for location, count in location_counts.items():
        print(f"  {location}: {count}")
    
    print(f"\nExtracted Solar Tracker Piles by Tracker Type:")
    tracker_counts = piles_df['tracker_type'].value_counts()
    for tracker_type, count in tracker_counts.items():
        print(f"  Tracker {tracker_type}: {count}")
    
    print(f"\n=== EXCLUDED PILE TYPES ===")
    print("The following pile types were EXCLUDED from extraction:")
    for layer in EXCLUDED_PILE_LAYERS:
        print(f"  {layer}")
    
    print(f"\n Total Solar Tracker Foundation Piles: {len(all_solar_tracker_piles)}")
    print(f" These are the piles that should be compared with PointNet++ detections.")
else:
    print("No solar tracker piles extracted.")


import geopandas as gpd
import pandas as pd
import numpy as np
from shapely.geometry import Point, Polygon
from pyproj import Transformer
import warnings

warnings.filterwarnings("ignore")  # Suppress geospatial and KML export warnings

# Transformer from CAD CRS (EPSG:3857) to WGS84 (EPSG:4326)
transformer = Transformer.from_crs("EPSG:3857", "EPSG:4326", always_xy=True)

# Define survey boundary in CAD projection (EPSG:3857)
survey_boundary_coords = [
    (2335367.6595, 10652597.6363),
    (2335624.5755, 10652597.5533),
    (2335624.5755, 10651698.6673),
    (2334666.9795, 10652112.1673),
    (2334666.9795, 10651698.5013)
]
survey_polygon = Polygon(survey_boundary_coords)

# Main data processing
if all_solar_tracker_piles:
    # Load and clean
    piles_df = pd.DataFrame(all_solar_tracker_piles)
    piles_df['z_numeric'] = pd.to_numeric(piles_df.get('z', 0.0), errors='coerce').fillna(0.0)

    # Survey inclusion check (still in CAD coords)
    piles_df['within_survey'] = piles_df.apply(
        lambda row: survey_polygon.contains(Point(row['x'], row['y'])), axis=1
    )

    # Summary stats
    total_piles = len(piles_df)
    piles_in_boundary = piles_df['within_survey'].sum()
    piles_outside_boundary = total_piles - piles_in_boundary

    print("Pile distribution within survey boundary:")
    print(piles_df.groupby(['layer', 'within_survey']).size().unstack(fill_value=0))
    print(f"\nSummary:")
    print(f"Total piles extracted: {total_piles}")
    print(f"Piles within survey boundary: {piles_in_boundary}")
    print(f"Piles outside survey boundary: {piles_outside_boundary}")
    print(f"Survey coverage: {piles_in_boundary / total_piles * 100:.1f}%")

    # Save filtered CSV
    surveyed_piles = piles_df[piles_df['within_survey']]
    if not surveyed_piles.empty:
        surveyed_piles.to_csv(out_path / f"{site_name.lower()}_piles_surveyed_{timestamp}.csv", index=False)
        print(f"\nSaved {len(surveyed_piles)} surveyed piles to CSV")

    # Coordinate transformation to WGS84
    transformed_coords = [transformer.transform(x, y) for x, y in zip(piles_df['x'], piles_df['y'])]
    piles_df['lon'], piles_df['lat'] = zip(*transformed_coords)
    piles_df['geometry'] = [Point(lon, lat) for lon, lat in transformed_coords]

    # Build GeoDataFrame with valid Points only
    gdf = gpd.GeoDataFrame(piles_df, geometry='geometry', crs="EPSG:4326")
    gdf = gdf[gdf.geometry.type == "Point"]
    gdf = gdf[gdf.is_valid]
    gdf['Name'] = gdf['layer'].astype(str)
    gdf['surveyed'] = piles_df['within_survey']

    # Export to KML
    out_file = out_path / f"{site_name.lower()}_cad_piles_{timestamp}.kml"
    gdf[['Name', 'geometry']].to_file(out_file, driver="KML")
    print(f"KML saved: {out_file}")

    # Export boundary polygon
    boundary_wgs = [transformer.transform(x, y) for x, y in survey_boundary_coords]
    boundary_wgs.append(boundary_wgs[0])  # Close the polygon
    boundary_polygon = Polygon(boundary_wgs)

    boundary_gdf = gpd.GeoDataFrame(
        [{"Name": "Surveyed Area", "geometry": boundary_polygon}],
        crs="EPSG:4326"
    )
    boundary_gdf = boundary_gdf[boundary_gdf.geometry.type == "Polygon"]
    boundary_gdf = boundary_gdf[boundary_gdf.is_valid]

    boundary_kml = out_path / f"{site_name.lower()}_survey_boundary_{timestamp}.kml"
    boundary_gdf.to_file(boundary_kml, driver="KML")
    print(f"Boundary KML saved: {boundary_kml}")

else:
    print("No pile data available for processing or KML export.")
