# Papermill parameters
site_name = "nortan_res"
project_type = "ENEL"
target_files = ["2024.08.26 - Norton_PILE-SP_All Piles_Preliminary.dxf"]
search_path = "../../../data/raw"
output_dir = "../../../preprocessed/nortan_res/cad_extraction"


import pandas as pd
import numpy as np
from pathlib import Path
import ezdxf
import json
import logging
from datetime import datetime
from collections import defaultdict

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

logger.info("Solar CAD extraction started")
logger.info(f"Site: {site_name}, Project: {project_type}")
logger.info(f"Target files: {target_files}")
logger.info(f"Search path: {search_path}")
logger.info(f"Output directory: {output_dir}")

!ls -lh ../../../

# Coordinate system range config (already saved from coordinate system detection)
def load_coordinate_config(config_dir="../../../output_runs/coordinate_analysis"):
    config_path = Path(config_dir)
    config_files = list(config_path.glob("*/site_coordinate_config_*.json"))
    if not config_files:
        logger.warning("No config found")
        return None
    latest = max(config_files, key=lambda f: f.stat().st_mtime)
    return json.load(open(latest))

coord_config = load_coordinate_config()
valid_ranges = coord_config.get("valid_ranges") if coord_config else None

def create_coordinate_validator(valid_ranges):
    if not valid_ranges:
        return lambda x, y, z=None: abs(x) > 100 and abs(y) > 100
    def validator(x, y, z=None):
        return (valid_ranges['x_min'] <= x <= valid_ranges['x_max'] and
                valid_ranges['y_min'] <= y <= valid_ranges['y_max'])
    return validator

is_valid_coordinate = create_coordinate_validator(valid_ranges)


import subprocess

def find_cad_files(search_path, target_files):
    found = []
    for name in target_files:
        result = subprocess.getoutput(f"find {search_path} -name '{name}'").strip()
        if result:
            found.append(Path(result))
    return found

cad_files = find_cad_files(search_path, target_files)
assert cad_files, "No CAD files found."
print(f"[INFO] Found {len(cad_files)} CAD file(s)")

from collections import Counter

layer_counts = Counter()
for f in cad_files:
    doc = ezdxf.readfile(f)
    msp = doc.modelspace()
    for e in msp:
        layer = getattr(e.dxf, 'layer', 'UNKNOWN')
        layer_counts[layer] += 1

print("\nTop layers in modelspace:")
for layer, count in layer_counts.most_common(20):
    print(f"{layer}: {count}")


print(f"Matched tracker layer: {layer}")  # inside extract_solar_trackers
print(f"Matched module layer: {layer}")   # inside extract_solar_modules


PILE_LAYERS = {
    "S-PILE_A_INT_BEAR", "S-PILE_B_INT_BEAR", "S-PILE_A_INT_SEISMIC", "S-PILE_B_INT_SEISMIC",
    "S-PILE_A_INT_GEAR", "S-PILE_B_INT_GEAR", "S-PILE_A_EXT_BEAR", "S-PILE_B_EXT_BEAR",
    "S-PILE_LBD", "S-PILE_INVERTER", "S-PILE_CAB-NS-INTER", "S-PILE_CAB_NS_TRANSITION",
    "S-PILE_CAB-NS", "S-PILE_CAB-EW", "S-PILE_A_EXT_SEISMIC", "S-PILE_B_EXT_SEISMIC"
}

# TRACKER_LAYERS = {"S-TRACKER_A", "S-TRACKER_B"}
# MODULE_LAYERS = {"E-ARRY-STR-24", "E-ARRY-STR-20"}

tracker_keywords = ['TRACKER', 'S-TRACKER_A', 'S-TRACKER_B']
module_keywords = ['ARRY', 'STR', 'PV', 'MOD', 'MODULE']
pile_keywords = ['PILE', 'BEAR', 'SEISMIC', 'CAB', 'INVERTER', 'LBD']


def extract_solar_trackers(entity, layer):
    if entity.dxftype() == 'INSERT':
        block_name = getattr(entity.dxf, 'name', '').upper()
        if any(k in block_name for k in tracker_keywords):
            p = entity.dxf.insert
            if is_valid_coordinate(p.x, p.y, p.z):
                print(f"Matched tracker block: {block_name}, layer: {layer}")
                return [{
                    'x': p.x, 'y': p.y, 'z': p.z,
                    'layer': layer,
                    'block_name': block_name,
                    'rotation': getattr(entity.dxf, 'rotation', 0.0),
                    'type': 'tracker'
                }]
    return []


def extract_solar_modules(entity, layer):
    layer_upper = layer.upper()
    if entity.dxftype() in ['LWPOLYLINE', 'POLYLINE'] and any(k in layer_upper for k in module_keywords):
        try:
            bbox = entity.bbox()
            if bbox:
                cx = (bbox[0].x + bbox[1].x) / 2
                cy = (bbox[0].y + bbox[1].y) / 2
                cz = (bbox[0].z + bbox[1].z) / 2
                if is_valid_coordinate(cx, cy, cz):
                    print(f"Matched module layer: {layer}")
                    return [{
                        'x': cx, 'y': cy, 'z': cz,
                        'layer': layer,
                        'x_min': bbox[0].x, 'x_max': bbox[1].x,
                        'y_min': bbox[0].y, 'y_max': bbox[1].y,
                        'width': bbox[1].x - bbox[0].x,
                        'height': bbox[1].y - bbox[0].y,
                        'block_name': '',
                        'type': 'module'
                    }]
        except:
            pass
    return []

def extract_pile_circles(entity, layer):
    layer_upper = layer.upper()
    if entity.dxftype() == 'CIRCLE' and any(k in layer_upper for k in pile_keywords):
        center = entity.dxf.center
        radius = entity.dxf.radius
        if is_valid_coordinate(center.x, center.y, center.z):
            # Try to find parent block reference (INSERT) if any
            parent_name = ''
            if hasattr(entity, 'block') and hasattr(entity.block, 'name'):
                parent_name = entity.block.name
            return [{
                'x': center.x, 'y': center.y, 'z': center.z,
                'radius': radius,
                'layer': layer,
                'block_name': parent_name,
                'type': 'pile'
            }]
    return []

from collections import Counter

entity_counter = Counter()

def process_cad_file(file_path):
    doc = ezdxf.readfile(file_path)
    msp = doc.modelspace()
    trackers = []
    modules = []
    piles = []
    for e in msp:
        layer = getattr(e.dxf, 'layer', 'UNKNOWN')
        trackers.extend(extract_solar_trackers(e, layer))
        modules.extend(extract_solar_modules(e, layer))
        piles.extend(extract_pile_circles(e, layer))
    return trackers, modules, piles

all_trackers = []
all_modules = []
all_piles = []

for f in cad_files:
    t, m, p = process_cad_file(f)
    for item in t + m + p:
        item['source_file'] = f.name
    all_trackers.extend(t)
    all_modules.extend(m)
    all_piles.extend(p)


timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
out_path = Path(output_dir)
out_path.mkdir(exist_ok=True, parents=True)

if all_trackers:
    pd.DataFrame(all_trackers).to_csv(out_path / f"{site_name.lower()}_trackers_{timestamp}.csv", index=False)
if all_modules:
    pd.DataFrame(all_modules).to_csv(out_path / f"{site_name.lower()}_modules_{timestamp}.csv", index=False)
if all_piles:
    pd.DataFrame(all_piles).to_csv(out_path / f"{site_name.lower()}_piles_{timestamp}.csv", index=False)

print(f"Extracted {len(all_trackers)} trackers, {len(all_modules)} modules, {len(all_piles)} piles.")
