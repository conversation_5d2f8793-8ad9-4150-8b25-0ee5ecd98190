{"cells": [{"cell_type": "markdown", "id": "header", "metadata": {}, "source": ["# Multi-Site PointNet++ Inference Runner\n", "\n", "This notebook uses Papermill to execute the PointNet++ inference notebook across multiple construction sites with different point cloud data sources.\n", "\n", "**Workflow:**\n", "1. Configure multiple sites with their point cloud paths\n", "2. Execute inference notebook for each site using Papermill\n", "3. Generate separate executed notebooks for each site\n", "4. Provide summary of results\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025  \n", "**Project**: As-Built Foundation Analysis - Multi-Site Inference"]}, {"cell_type": "code", "execution_count": 1, "id": "imports", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Imports completed successfully\n"]}], "source": ["import os\n", "import sys\n", "import papermill as pm\n", "from pathlib import Path\n", "from datetime import datetime\n", "import pandas as pd\n", "\n", "print(\"Imports completed successfully\")"]}, {"cell_type": "code", "execution_count": 2, "id": "configuration", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Configured 2 sites for inference:\n", "  1. althea_rpcs: Althea RPCS site with Point_Cloud.las\n", "  2. nortan_res: Nortan RES site with Block_11_2m.las\n"]}], "source": ["# Site configurations for inference\n", "SITE_CONFIGS = [\n", "    {\n", "        \"site_name\": \"althea_rpcs\",\n", "        \"point_cloud_path\": \"../../../../../data/raw/althea_rpcs/pointcloud/Point_Cloud.las\",\n", "        \"description\": \"Althea RPCS site with Point_Cloud.las\"\n", "    },\n", "    {\n", "        \"site_name\": \"nortan_res\",\n", "        \"point_cloud_path\": \"../../../../../data/raw/nortan_res/pointcloud/Block_11_2m.las\",\n", "        \"description\": \"Nortan RES site with Block_11_2m.las\"\n", "    }\n", "]\n", "\n", "# Base parameters for all runs\n", "BASE_PARAMETERS = {\n", "    \"MODEL_PATH\": \"best_pointnet_plus_plus.pth\",\n", "    \"DWG_PATH\": \"\",\n", "    \"CONFIDENCE_THRESHOLD\": 0.95,\n", "    \"BATCH_SIZE\": 16,\n", "    \"GRID_SPACING\": 5.0,\n", "    \"PATCH_SIZE\": 3.0,\n", "    \"NUM_POINTS\": 128,\n", "    \"EXPERIMENT_NAME\": \"pointnet_plus_plus_inference\"\n", "}\n", "\n", "print(f\"Configured {len(SITE_CONFIGS)} sites for inference:\")\n", "for i, config in enumerate(SITE_CONFIGS, 1):\n", "    print(f\"  {i}. {config['site_name']}: {config['description']}\")"]}, {"cell_type": "code", "execution_count": 3, "id": "validation", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Validating file paths...\n", "All paths validated successfully\n", "Output directory ready\n"]}], "source": ["def validate_paths():\n", "    \"\"\"Validate that required files exist before execution\"\"\"\n", "    issues = []\n", "    \n", "    # Check if base notebook exists\n", "    base_notebook = \"02_pointnet_plus_plus_inference.ipynb\"\n", "    if not Path(base_notebook).exists():\n", "        issues.append(f\"Base notebook not found: {base_notebook}\")\n", "    \n", "    # Check if model exists\n", "    model_path = BASE_PARAMETERS[\"MODEL_PATH\"]\n", "    if not Path(model_path).exists():\n", "        issues.append(f\"Model file not found: {model_path}\")\n", "    \n", "    # Check point cloud files\n", "    for config in SITE_CONFIGS:\n", "        pc_path = config[\"point_cloud_path\"]\n", "        if not Path(pc_path).exists():\n", "            issues.append(f\"Point cloud not found for {config['site_name']}: {pc_path}\")\n", "    \n", "    return issues\n", "\n", "# Validate paths before starting\n", "print(\"Validating file paths...\")\n", "validation_issues = validate_paths()\n", "\n", "if validation_issues:\n", "    print(\"Validation failed:\")\n", "    for issue in validation_issues:\n", "        print(f\"  - {issue}\")\n", "    print(\"\\nPlease fix these issues before proceeding.\")\n", "else:\n", "    print(\"All paths validated successfully\")\n", "    \n", "# Create output directory if it doesn't exist\n", "os.makedirs(\"output_runs/pointnet_plus_plus_inference\", exist_ok=True)\n", "print(\"Output directory ready\")"]}, {"cell_type": "code", "execution_count": 9, "id": "execution_functions", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Inference runner ready.\n"]}], "source": ["import os\n", "\n", "def run_site_inference(site_config):\n", "    \"\"\"Run inference notebook for a given site using papermill shell command.\"\"\"\n", "    site = site_config[\"site_name\"]\n", "    input_nb = \"02_pointnet_plus_plus_inference.ipynb\"\n", "    output_nb = f\"02_pointnet_plus_plus_inference_{site}_executed.ipynb\"\n", "    output_dir = f\"output_runs/pointnet_plus_plus_inference/{site}\"\n", "    \n", "    print(f\"\\n--- Running inference: {site} ---\")\n", "    \n", "    cmd = (\n", "        f'papermill {input_nb} {output_nb} '\n", "        f'-p NEW_SITE_NAME \"{site}\" '\n", "        f'-p POINT_CLOUD_PATH \"{site_config[\"point_cloud_path\"]}\" '\n", "        f'-p OUTPUT_DIR \"{output_dir}\" '\n", "        f'-p RUN_NAME \"inference_{site}\" '\n", "        '--log-output --kernel pytorch-geo-dev'\n", "    )\n", "\n", "    try:\n", "        if os.system(cmd) == 0:\n", "            print(f\"✓ Completed: {site}\")\n", "            return True, None\n", "        else:\n", "            msg = f\"Papermill failed for {site}\"\n", "            print(msg)\n", "            return False, msg\n", "    except Exception as e:\n", "        print(f\"Exception: {e}\")\n", "        return False, str(e)\n", "\n", "print(\"Inference runner ready.\")"]}, {"cell_type": "code", "execution_count": 10, "id": "execute_sites", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Starting multi-site inference...\n", "==================================================\n", "\n", "--- Running inference: althea_rpcs ---\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Input Notebook:  02_pointnet_plus_plus_inference.ipynb\n", "Output Notebook: 02_pointnet_plus_plus_inference_althea_rpcs_executed.ipynb\n", "Executing notebook with kernel: pytorch-geo-dev\n", "Executing Cell 1---------------------------------------\n", "Ending Cell 1------------------------------------------\n", "Executing Cell 2---------------------------------------\n", "Requirement already satisfied: mlflow in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (3.1.1)\n", "Requirement already satisfied: mlflow-skinny==3.1.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow) (3.1.1)\n", "Requirement already satisfied: Flask<4 in /Users/<USER>/.local/lib/python3.11/site-packages (from mlflow) (3.1.1)\n", "Requirement already satisfied: alembic!=1.10.0,<2 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow) (1.16.2)\n", "Requirement already satisfied: docker<8,>=4.0.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow) (7.1.0)\n", "Requirement already satisfied: graphene<4 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow) (3.4.3)\n", "Requirement already satisfied: gunicorn<24 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow) (23.0.0)\n", "Requirement already satisfied: matplotlib<4 in /Users/<USER>/.local/lib/python3.11/site-packages (from mlflow) (3.10.3)\n", "Requirement already satisfied: numpy<3 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow) (1.26.4)\n", "Requirement already satisfied: pandas<3 in /Users/<USER>/.local/lib/python3.11/site-packages (from mlflow) (2.3.0)\n", "Requirement already satisfied: pyarrow<21,>=4.0.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow) (20.0.0)\n", "Requirement already satisfied: scikit-learn<2 in /Users/<USER>/.local/lib/python3.11/site-packages (from mlflow) (1.7.0)\n", "Requirement already satisfied: scipy<2 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow) (1.10.1)\n", "Requirement already satisfied: sqlalchemy<3,>=1.4.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow) (2.0.41)\n", "\n", "Requirement already satisfied: cachetools<7,>=5.0.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (5.5.2)\n", "Requirement already satisfied: click<9,>=7.0 in /Users/<USER>/.local/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (8.2.1)\n", "Requirement already satisfied: cloudpickle<4 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (3.1.1)\n", "Requirement already satisfied: databricks-sdk<1,>=0.20.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (0.57.0)\n", "Requirement already satisfied: fastapi<1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (0.115.14)\n", "Requirement already satisfied: gitpython<4,>=3.1.9 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (3.1.44)\n", "Requirement already satisfied: importlib_metadata!=4.7.0,<9,>=3.7.0 in /Users/<USER>/.local/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (8.7.0)\n", "Requirement already satisfied: opentelemetry-api<3,>=1.9.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (1.34.1)\n", "Requirement already satisfied: opentelemetry-sdk<3,>=1.9.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (1.34.1)\n", "Requirement already satisfied: packaging<26 in /Users/<USER>/.local/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (25.0)\n", "Requirement already satisfied: protobuf<7,>=3.12.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (3.20.3)\n", "Requirement already satisfied: pydantic<3,>=1.10.8 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (2.11.7)\n", "Requirement already satisfied: pyyaml<7,>=5.1 in /Users/<USER>/.local/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (6.0.2)\n", "Requirement already satisfied: requests<3,>=2.17.3 in /Users/<USER>/.local/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (2.32.4)\n", "Requirement already satisfied: sqlparse<1,>=0.4.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (0.5.3)\n", "Requirement already satisfied: typing-extensions<5,>=4.0.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (4.14.0)\n", "Requirement already satisfied: uvicorn<1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (0.34.3)\n", "Requirement already satisfied: <PERSON><PERSON> in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from alembic!=1.10.0,<2->mlflow) (1.3.10)\n", "Requirement already satisfied: google-auth~=2.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from databricks-sdk<1,>=0.20.0->mlflow-skinny==3.1.1->mlflow) (2.40.3)\n", "Requirement already satisfied: urllib3>=1.26.0 in /Users/<USER>/.local/lib/python3.11/site-packages (from docker<8,>=4.0.0->mlflow) (2.5.0)\n", "Requirement already satisfied: starlette<0.47.0,>=0.40.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from fastapi<1->mlflow-skinny==3.1.1->mlflow) (0.46.2)\n", "Requirement already satisfied: blinker>=1.9.0 in /Users/<USER>/.local/lib/python3.11/site-packages (from Flask<4->mlflow) (1.9.0)\n", "Requirement already satisfied: itsdangerous>=2.2.0 in /Users/<USER>/.local/lib/python3.11/site-packages (from Flask<4->mlflow) (2.2.0)\n", "Requirement already satisfied: jinja2>=3.1.2 in /Users/<USER>/.local/lib/python3.11/site-packages (from Flask<4->mlflow) (3.1.6)\n", "Requirement already satisfied: markupsafe>=2.1.1 in /Users/<USER>/.local/lib/python3.11/site-packages (from Flask<4->mlflow) (3.0.2)\n", "Requirement already satisfied: werkzeug>=3.1.0 in /Users/<USER>/.local/lib/python3.11/site-packages (from Flask<4->mlflow) (3.1.3)\n", "Requirement already satisfied: gitdb<5,>=4.0.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from gitpython<4,>=3.1.9->mlflow-skinny==3.1.1->mlflow) (4.0.12)\n", "Requirement already satisfied: smmap<6,>=3.0.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from gitdb<5,>=4.0.1->gitpython<4,>=3.1.9->mlflow-skinny==3.1.1->mlflow) (5.0.2)\n", "Requirement already satisfied: pyasn1-modules>=0.2.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from google-auth~=2.0->databricks-sdk<1,>=0.20.0->mlflow-skinny==3.1.1->mlflow) (0.4.2)\n", "Requirement already satisfied: rsa<5,>=3.1.4 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from google-auth~=2.0->databricks-sdk<1,>=0.20.0->mlflow-skinny==3.1.1->mlflow) (4.9.1)\n", "Requirement already satisfied: graphql-core<3.3,>=3.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from graphene<4->mlflow) (3.2.6)\n", "Requirement already satisfied: graphql-relay<3.3,>=3.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from graphene<4->mlflow) (3.2.0)\n", "Requirement already satisfied: python-dateutil<3,>=2.7.0 in /Users/<USER>/.local/lib/python3.11/site-packages (from graphene<4->mlflow) (2.9.0.post0)\n", "Requirement already satisfied: zipp>=3.20 in /Users/<USER>/.local/lib/python3.11/site-packages (from importlib_metadata!=4.7.0,<9,>=3.7.0->mlflow-skinny==3.1.1->mlflow) (3.23.0)\n", "Requirement already satisfied: contourpy>=1.0.1 in /Users/<USER>/.local/lib/python3.11/site-packages (from matplotlib<4->mlflow) (1.3.2)\n", "Requirement already satisfied: cycler>=0.10 in /Users/<USER>/.local/lib/python3.11/site-packages (from matplotlib<4->mlflow) (0.12.1)\n", "Requirement already satisfied: fonttools>=4.22.0 in /Users/<USER>/.local/lib/python3.11/site-packages (from matplotlib<4->mlflow) (4.58.4)\n", "Requirement already satisfied: kiwisolver>=1.3.1 in /Users/<USER>/.local/lib/python3.11/site-packages (from matplotlib<4->mlflow) (1.4.8)\n", "Requirement already satisfied: pillow>=8 in /Users/<USER>/.local/lib/python3.11/site-packages (from matplotlib<4->mlflow) (11.3.0)\n", "Requirement already satisfied: pyparsing>=2.3.1 in /Users/<USER>/.local/lib/python3.11/site-packages (from matplotlib<4->mlflow) (3.2.3)\n", "Requirement already satisfied: opentelemetry-semantic-conventions==0.55b1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from opentelemetry-sdk<3,>=1.9.0->mlflow-skinny==3.1.1->mlflow) (0.55b1)\n", "Requirement already satisfied: pytz>=2020.1 in /Users/<USER>/.local/lib/python3.11/site-packages (from pandas<3->mlflow) (2025.2)\n", "Requirement already satisfied: tzdata>=2022.7 in /Users/<USER>/.local/lib/python3.11/site-packages (from pandas<3->mlflow) (2025.2)\n", "\n", "Requirement already satisfied: annotated-types>=0.6.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from pydantic<3,>=1.10.8->mlflow-skinny==3.1.1->mlflow) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.2 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from pydantic<3,>=1.10.8->mlflow-skinny==3.1.1->mlflow) (2.33.2)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from pydantic<3,>=1.10.8->mlflow-skinny==3.1.1->mlflow) (0.4.1)\n", "Requirement already satisfied: six>=1.5 in /Users/<USER>/.local/lib/python3.11/site-packages (from python-dateutil<3,>=2.7.0->graphene<4->mlflow) (1.17.0)\n", "Requirement already satisfied: charset_normalizer<4,>=2 in /Users/<USER>/.local/lib/python3.11/site-packages (from requests<3,>=2.17.3->mlflow-skinny==3.1.1->mlflow) (3.4.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /Users/<USER>/.local/lib/python3.11/site-packages (from requests<3,>=2.17.3->mlflow-skinny==3.1.1->mlflow) (3.10)\n", "Requirement already satisfied: certifi>=2017.4.17 in /Users/<USER>/.local/lib/python3.11/site-packages (from requests<3,>=2.17.3->mlflow-skinny==3.1.1->mlflow) (2025.6.15)\n", "Requirement already satisfied: pyasn1>=0.1.3 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from rsa<5,>=3.1.4->google-auth~=2.0->databricks-sdk<1,>=0.20.0->mlflow-skinny==3.1.1->mlflow) (0.6.1)\n", "Requirement already satisfied: joblib>=1.2.0 in /Users/<USER>/.local/lib/python3.11/site-packages (from scikit-learn<2->mlflow) (1.5.1)\n", "Requirement already satisfied: threadpoolctl>=3.1.0 in /Users/<USER>/.local/lib/python3.11/site-packages (from scikit-learn<2->mlflow) (3.6.0)\n", "Requirement already satisfied: anyio<5,>=3.6.2 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from starlette<0.47.0,>=0.40.0->fastapi<1->mlflow-skinny==3.1.1->mlflow) (4.9.0)\n", "Requirement already satisfied: sniffio>=1.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from anyio<5,>=3.6.2->starlette<0.47.0,>=0.40.0->fastapi<1->mlflow-skinny==3.1.1->mlflow) (1.3.1)\n", "Requirement already satisfied: h11>=0.8 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from uvicorn<1->mlflow-skinny==3.1.1->mlflow) (0.16.0)\n", "\n", "Ending Cell 2------------------------------------------\n", "Executing Cell 3---------------------------------------\n", "total 266624\n", "-rw-r--r--@ 1 <USER>  <GROUP>   130M Sep 20  2024 Croped_pointcloud_FLy4_Giorgio.las\n", "\n", "Ending Cell 3------------------------------------------\n", "Executing Cell 4---------------------------------------\n", "ls: ../pointnet_plus_plus/: No such file or directory\n", "\n", "Ending Cell 4------------------------------------------\n", "Executing Cell 5---------------------------------------\n", "Ending Cell 5------------------------------------------\n", "Executing Cell 6---------------------------------------\n", "Ending Cell 6------------------------------------------\n", "Executing Cell 7---------------------------------------\n", "New Site Analysis Configuration:\n", "Site Name: althea_rpcs\n", "Patch Size: 3.0m radius\n", "Points per Patch: 128\n", "Model Path: best_pointnet_plus_plus.pth\n", "Point Cloud Path: ../../../../../data/raw/althea_rpcs/pointcloud/Point_Cloud.las\n", "Output Directory: output_runs/pointnet_plus_plus_inference/althea_rpcs\n", "\n", "Ending Cell 7------------------------------------------\n", "Executing Cell 8---------------------------------------\n", "No handler found for comm target 'dash'\n", "Ending Cell 8------------------------------------------\n", "Executing Cell 9---------------------------------------\n", "2025/07/28 17:40:48 INFO mlflow.tracking.fluent: Experiment with name 'pointnet_plus_plus_inference' does not exist. Creating a new experiment.\n", "\n", "Output directory created: output_runs/pointnet_plus_plus_inference/althea_rpcs\n", "MLflow experiment initialized\n", "\n", "Ending Cell 9------------------------------------------\n", "Executing Cell 10--------------------------------------\n", "Ending Cell 10-----------------------------------------\n", "Executing Cell 11--------------------------------------\n", "Ending Cell 11-----------------------------------------\n", "Executing Cell 12--------------------------------------\n", "All file paths validated successfully.\n", "\n", "Ending Cell 12-----------------------------------------\n", "Executing Cell 13--------------------------------------\n", "Ending Cell 13-----------------------------------------\n", "Executing Cell 14--------------------------------------\n", "Using device: cpu\n", "Loaded trained PointNet++ model from best_pointnet_plus_plus.pth\n", "Model has 1,465,154 parameters\n", "\n", "Ending Cell 14-----------------------------------------\n", "Executing Cell 15--------------------------------------\n", "Ending Cell 15-----------------------------------------\n", "Executing Cell 16--------------------------------------\n", "Loaded LAS file with 52,862,386 points\n", "Point cloud bounds:\n", "  X: 599595.18 to 599866.15\n", "  Y: 4334366.65 to 4334660.84\n", "\n", "  Z: 238.63 to 259.18\n", "\n", "Ending Cell 16-----------------------------------------\n", "Executing Cell 17--------------------------------------\n", "Ending Cell 17-----------------------------------------\n", "Executing Cell 18--------------------------------------\n", "Ending Cell 18-----------------------------------------\n", "Executing Cell 19--------------------------------------\n", "Ending Cell 19-----------------------------------------\n", "Executing Cell 20--------------------------------------\n", "Point cloud density: 663.12 points/m²\n", "\n", "Average spacing between points: 0.016 m\n", "\n", "Ending Cell 20-----------------------------------------\n", "Executing Cell 21--------------------------------------\n", "Created analysis grid with 3360 analysis points\n", "Grid spacing: 5.0m\n", "\n", "Filtered to 2779 valid analysis points\n", "\n", "Successfully extracted sample patch with shape: (128, 3)\n", "Sample patch statistics:\n", "  X range: -0.011 to 0.012\n", "  Y range: -0.011 to 0.006\n", "  Z range: 1.000 to 1.000\n", "\n", "Ending Cell 21-----------------------------------------\n", "Executing Cell 22--------------------------------------\n", "Sample patch inference:\n", "  Pile probability: 0.9999\n", "  Prediction: PILE\n", "  Confidence threshold: 0.95\n", "\n", "Ending Cell 22-----------------------------------------\n", "Executing Cell 23--------------------------------------\n", "Ending Cell 23-----------------------------------------\n", "Executing Cell 24--------------------------------------\n", "Running site-wide analysis on sample grid...\n", "Grid points: 3360\n", "Using first 2000 points for testing\n", "\n", "Processed 1519 analysis points\n", "Analysis Results Summary:\n", "  Total analysis points: 1519\n", "  Pile detections: 1519 (100.0%)\n", "  Non-pile areas: 0 (0.0%)\n", "  Average pile probability: 0.9998\n", "  High confidence piles (>0.9): 1519\n", "\n", "Ending Cell 24-----------------------------------------\n", "Executing Cell 25--------------------------------------\n", "   grid_index             x             y  pile_probability prediction\n", "0          10  599592.67945  4.334414e+06          0.999790       PILE\n", "1          13  599592.67945  4.334429e+06          0.999792       PILE\n", "2          14  599592.67945  4.334434e+06          0.999792       PILE\n", "3          16  599592.67945  4.334444e+06          0.999791       PILE\n", "4          19  599592.67945  4.334459e+06          0.999798       PILE\n", "\n", "Ending Cell 25-----------------------------------------\n", "Executing Cell 26--------------------------------------\n", "Point Cloud Bounds (X, Y): [ 599595.17945018 4334366.64733374] [ 599866.15345018 4334660.83633374]\n", "\n", "Ending Cell 26-----------------------------------------\n", "Executing Cell 27--------------------------------------\n", "Ending Cell 27-----------------------------------------\n", "<PERSON><PERSON> died while waiting for execute reply.\n", "\n", "Aborted!\n", "Task exception was never retrieved\n", "future: <Task finished name='Task-105' coro=<NotebookClient.async_execute_cell() done, defined at /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/nbclient/client.py:920> exception=DeadKernelError('<PERSON><PERSON> died')>\n", "Traceback (most recent call last):\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/nbclient/client.py\", line 1009, in async_execute_cell\n", "    raise Dead<PERSON><PERSON><PERSON><PERSON><PERSON>(\"<PERSON><PERSON> died\") from None\n", "nbclient.exceptions.DeadKernelError: <PERSON><PERSON> died\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Papermill failed for althea_rpcs\n", "\n", "--- Running inference: nortan_res ---\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Input Notebook:  02_pointnet_plus_plus_inference.ipynb\n", "Output Notebook: 02_pointnet_plus_plus_inference_nortan_res_executed.ipynb\n", "Executing notebook with kernel: pytorch-geo-dev\n", "Executing Cell 1---------------------------------------\n", "Ending Cell 1------------------------------------------\n", "Executing Cell 2---------------------------------------\n", "Requirement already satisfied: mlflow in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (3.1.1)\n", "\n", "Requirement already satisfied: mlflow-skinny==3.1.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow) (3.1.1)\n", "Requirement already satisfied: Flask<4 in /Users/<USER>/.local/lib/python3.11/site-packages (from mlflow) (3.1.1)\n", "Requirement already satisfied: alembic!=1.10.0,<2 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow) (1.16.2)\n", "Requirement already satisfied: docker<8,>=4.0.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow) (7.1.0)\n", "Requirement already satisfied: graphene<4 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow) (3.4.3)\n", "Requirement already satisfied: gunicorn<24 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow) (23.0.0)\n", "Requirement already satisfied: matplotlib<4 in /Users/<USER>/.local/lib/python3.11/site-packages (from mlflow) (3.10.3)\n", "Requirement already satisfied: numpy<3 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow) (1.26.4)\n", "Requirement already satisfied: pandas<3 in /Users/<USER>/.local/lib/python3.11/site-packages (from mlflow) (2.3.0)\n", "Requirement already satisfied: pyarrow<21,>=4.0.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow) (20.0.0)\n", "Requirement already satisfied: scikit-learn<2 in /Users/<USER>/.local/lib/python3.11/site-packages (from mlflow) (1.7.0)\n", "Requirement already satisfied: scipy<2 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow) (1.10.1)\n", "Requirement already satisfied: sqlalchemy<3,>=1.4.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow) (2.0.41)\n", "Requirement already satisfied: cachetools<7,>=5.0.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (5.5.2)\n", "Requirement already satisfied: click<9,>=7.0 in /Users/<USER>/.local/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (8.2.1)\n", "Requirement already satisfied: cloudpickle<4 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (3.1.1)\n", "Requirement already satisfied: databricks-sdk<1,>=0.20.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (0.57.0)\n", "Requirement already satisfied: fastapi<1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (0.115.14)\n", "Requirement already satisfied: gitpython<4,>=3.1.9 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (3.1.44)\n", "Requirement already satisfied: importlib_metadata!=4.7.0,<9,>=3.7.0 in /Users/<USER>/.local/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (8.7.0)\n", "Requirement already satisfied: opentelemetry-api<3,>=1.9.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (1.34.1)\n", "Requirement already satisfied: opentelemetry-sdk<3,>=1.9.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (1.34.1)\n", "Requirement already satisfied: packaging<26 in /Users/<USER>/.local/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (25.0)\n", "Requirement already satisfied: protobuf<7,>=3.12.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (3.20.3)\n", "Requirement already satisfied: pydantic<3,>=1.10.8 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (2.11.7)\n", "Requirement already satisfied: pyyaml<7,>=5.1 in /Users/<USER>/.local/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (6.0.2)\n", "Requirement already satisfied: requests<3,>=2.17.3 in /Users/<USER>/.local/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (2.32.4)\n", "Requirement already satisfied: sqlparse<1,>=0.4.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (0.5.3)\n", "Requirement already satisfied: typing-extensions<5,>=4.0.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (4.14.0)\n", "Requirement already satisfied: uvicorn<1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (0.34.3)\n", "Requirement already satisfied: <PERSON><PERSON> in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from alembic!=1.10.0,<2->mlflow) (1.3.10)\n", "Requirement already satisfied: google-auth~=2.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from databricks-sdk<1,>=0.20.0->mlflow-skinny==3.1.1->mlflow) (2.40.3)\n", "Requirement already satisfied: urllib3>=1.26.0 in /Users/<USER>/.local/lib/python3.11/site-packages (from docker<8,>=4.0.0->mlflow) (2.5.0)\n", "Requirement already satisfied: starlette<0.47.0,>=0.40.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from fastapi<1->mlflow-skinny==3.1.1->mlflow) (0.46.2)\n", "Requirement already satisfied: blinker>=1.9.0 in /Users/<USER>/.local/lib/python3.11/site-packages (from Flask<4->mlflow) (1.9.0)\n", "Requirement already satisfied: itsdangerous>=2.2.0 in /Users/<USER>/.local/lib/python3.11/site-packages (from Flask<4->mlflow) (2.2.0)\n", "Requirement already satisfied: jinja2>=3.1.2 in /Users/<USER>/.local/lib/python3.11/site-packages (from Flask<4->mlflow) (3.1.6)\n", "Requirement already satisfied: markupsafe>=2.1.1 in /Users/<USER>/.local/lib/python3.11/site-packages (from Flask<4->mlflow) (3.0.2)\n", "Requirement already satisfied: werkzeug>=3.1.0 in /Users/<USER>/.local/lib/python3.11/site-packages (from Flask<4->mlflow) (3.1.3)\n", "Requirement already satisfied: gitdb<5,>=4.0.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from gitpython<4,>=3.1.9->mlflow-skinny==3.1.1->mlflow) (4.0.12)\n", "Requirement already satisfied: smmap<6,>=3.0.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from gitdb<5,>=4.0.1->gitpython<4,>=3.1.9->mlflow-skinny==3.1.1->mlflow) (5.0.2)\n", "Requirement already satisfied: pyasn1-modules>=0.2.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from google-auth~=2.0->databricks-sdk<1,>=0.20.0->mlflow-skinny==3.1.1->mlflow) (0.4.2)\n", "Requirement already satisfied: rsa<5,>=3.1.4 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from google-auth~=2.0->databricks-sdk<1,>=0.20.0->mlflow-skinny==3.1.1->mlflow) (4.9.1)\n", "Requirement already satisfied: graphql-core<3.3,>=3.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from graphene<4->mlflow) (3.2.6)\n", "Requirement already satisfied: graphql-relay<3.3,>=3.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from graphene<4->mlflow) (3.2.0)\n", "Requirement already satisfied: python-dateutil<3,>=2.7.0 in /Users/<USER>/.local/lib/python3.11/site-packages (from graphene<4->mlflow) (2.9.0.post0)\n", "Requirement already satisfied: zipp>=3.20 in /Users/<USER>/.local/lib/python3.11/site-packages (from importlib_metadata!=4.7.0,<9,>=3.7.0->mlflow-skinny==3.1.1->mlflow) (3.23.0)\n", "Requirement already satisfied: contourpy>=1.0.1 in /Users/<USER>/.local/lib/python3.11/site-packages (from matplotlib<4->mlflow) (1.3.2)\n", "Requirement already satisfied: cycler>=0.10 in /Users/<USER>/.local/lib/python3.11/site-packages (from matplotlib<4->mlflow) (0.12.1)\n", "Requirement already satisfied: fonttools>=4.22.0 in /Users/<USER>/.local/lib/python3.11/site-packages (from matplotlib<4->mlflow) (4.58.4)\n", "Requirement already satisfied: kiwisolver>=1.3.1 in /Users/<USER>/.local/lib/python3.11/site-packages (from matplotlib<4->mlflow) (1.4.8)\n", "Requirement already satisfied: pillow>=8 in /Users/<USER>/.local/lib/python3.11/site-packages (from matplotlib<4->mlflow) (11.3.0)\n", "Requirement already satisfied: pyparsing>=2.3.1 in /Users/<USER>/.local/lib/python3.11/site-packages (from matplotlib<4->mlflow) (3.2.3)\n", "Requirement already satisfied: opentelemetry-semantic-conventions==0.55b1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from opentelemetry-sdk<3,>=1.9.0->mlflow-skinny==3.1.1->mlflow) (0.55b1)\n", "\n", "Requirement already satisfied: pytz>=2020.1 in /Users/<USER>/.local/lib/python3.11/site-packages (from pandas<3->mlflow) (2025.2)\n", "Requirement already satisfied: tzdata>=2022.7 in /Users/<USER>/.local/lib/python3.11/site-packages (from pandas<3->mlflow) (2025.2)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from pydantic<3,>=1.10.8->mlflow-skinny==3.1.1->mlflow) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.2 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from pydantic<3,>=1.10.8->mlflow-skinny==3.1.1->mlflow) (2.33.2)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from pydantic<3,>=1.10.8->mlflow-skinny==3.1.1->mlflow) (0.4.1)\n", "Requirement already satisfied: six>=1.5 in /Users/<USER>/.local/lib/python3.11/site-packages (from python-dateutil<3,>=2.7.0->graphene<4->mlflow) (1.17.0)\n", "Requirement already satisfied: charset_normalizer<4,>=2 in /Users/<USER>/.local/lib/python3.11/site-packages (from requests<3,>=2.17.3->mlflow-skinny==3.1.1->mlflow) (3.4.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /Users/<USER>/.local/lib/python3.11/site-packages (from requests<3,>=2.17.3->mlflow-skinny==3.1.1->mlflow) (3.10)\n", "Requirement already satisfied: certifi>=2017.4.17 in /Users/<USER>/.local/lib/python3.11/site-packages (from requests<3,>=2.17.3->mlflow-skinny==3.1.1->mlflow) (2025.6.15)\n", "Requirement already satisfied: pyasn1>=0.1.3 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from rsa<5,>=3.1.4->google-auth~=2.0->databricks-sdk<1,>=0.20.0->mlflow-skinny==3.1.1->mlflow) (0.6.1)\n", "Requirement already satisfied: joblib>=1.2.0 in /Users/<USER>/.local/lib/python3.11/site-packages (from scikit-learn<2->mlflow) (1.5.1)\n", "Requirement already satisfied: threadpoolctl>=3.1.0 in /Users/<USER>/.local/lib/python3.11/site-packages (from scikit-learn<2->mlflow) (3.6.0)\n", "Requirement already satisfied: anyio<5,>=3.6.2 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from starlette<0.47.0,>=0.40.0->fastapi<1->mlflow-skinny==3.1.1->mlflow) (4.9.0)\n", "Requirement already satisfied: sniffio>=1.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from anyio<5,>=3.6.2->starlette<0.47.0,>=0.40.0->fastapi<1->mlflow-skinny==3.1.1->mlflow) (1.3.1)\n", "Requirement already satisfied: h11>=0.8 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from uvicorn<1->mlflow-skinny==3.1.1->mlflow) (0.16.0)\n", "\n", "Ending Cell 2------------------------------------------\n", "Executing Cell 3---------------------------------------\n", "total 266624\n", "-rw-r--r--@ 1 <USER>  <GROUP>   130M Sep 20  2024 Croped_pointcloud_FLy4_Giorgio.las\n", "\n", "Ending Cell 3------------------------------------------\n", "Executing Cell 4---------------------------------------\n", "ls: ../pointnet_plus_plus/: No such file or directory\n", "\n", "Ending Cell 4------------------------------------------\n", "Executing Cell 5---------------------------------------\n", "Ending Cell 5------------------------------------------\n", "Executing Cell 6---------------------------------------\n", "Ending Cell 6------------------------------------------\n", "Executing Cell 7---------------------------------------\n", "New Site Analysis Configuration:\n", "Site Name: nortan_res\n", "Patch Size: 3.0m radius\n", "Points per Patch: 128\n", "Model Path: best_pointnet_plus_plus.pth\n", "Point Cloud Path: ../../../../../data/raw/nortan_res/pointcloud/Block_11_2m.las\n", "Output Directory: output_runs/pointnet_plus_plus_inference/nortan_res\n", "\n", "Ending Cell 7------------------------------------------\n", "Executing Cell 8---------------------------------------\n", "No handler found for comm target 'dash'\n", "Ending Cell 8------------------------------------------\n", "Executing Cell 9---------------------------------------\n", "Output directory created: output_runs/pointnet_plus_plus_inference/nortan_res\n", "MLflow experiment initialized\n", "\n", "Ending Cell 9------------------------------------------\n", "Executing Cell 10--------------------------------------\n", "Ending Cell 10-----------------------------------------\n", "Executing Cell 11--------------------------------------\n", "Ending Cell 11-----------------------------------------\n", "Executing Cell 12--------------------------------------\n", "All file paths validated successfully.\n", "\n", "Ending Cell 12-----------------------------------------\n", "Executing Cell 13--------------------------------------\n", "Ending Cell 13-----------------------------------------\n", "Executing Cell 14--------------------------------------\n", "Using device: cpu\n", "Loaded trained PointNet++ model from best_pointnet_plus_plus.pth\n", "Model has 1,465,154 parameters\n", "\n", "Ending Cell 14-----------------------------------------\n", "Executing Cell 15--------------------------------------\n", "Ending Cell 15-----------------------------------------\n", "Executing Cell 16--------------------------------------\n", "Loaded LAS file with 35,565,352 points\n", "Point cloud bounds:\n", "  X: 385723.95 to 385809.63\n", "  Y: 3529182.83 to 3529447.01\n", "  Z: 553.38 to 556.27\n", "\n", "Ending Cell 16-----------------------------------------\n", "Executing Cell 17--------------------------------------\n", "Ending Cell 17-----------------------------------------\n", "Executing Cell 18--------------------------------------\n", "Ending Cell 18-----------------------------------------\n", "Executing Cell 19--------------------------------------\n", "Ending Cell 19-----------------------------------------\n", "Executing Cell 20--------------------------------------\n", "Point cloud density: 1571.40 points/m²\n", "\n", "Average spacing between points: 0.010 m\n", "\n", "Ending Cell 20-----------------------------------------\n", "Executing Cell 21--------------------------------------\n", "Created analysis grid with 1026 analysis points\n", "Grid spacing: 5.0m\n", "\n", "Filtered to 888 valid analysis points\n", "\n", "Successfully extracted sample patch with shape: (128, 3)\n", "Sample patch statistics:\n", "  X range: -0.004 to -0.002\n", "  Y range: 0.004 to 0.005\n", "  Z range: 1.000 to 1.000\n", "\n", "Ending Cell 21-----------------------------------------\n", "Executing Cell 22--------------------------------------\n", "Sample patch inference:\n", "  Pile probability: 0.9998\n", "  Prediction: PILE\n", "  Confidence threshold: 0.95\n", "\n", "Ending Cell 22-----------------------------------------\n", "Executing Cell 23--------------------------------------\n", "Ending Cell 23-----------------------------------------\n", "Executing Cell 24--------------------------------------\n", "Running site-wide analysis on sample grid...\n", "Grid points: 1026\n", "\n", "Processed 888 analysis points\n", "Analysis Results Summary:\n", "  Total analysis points: 888\n", "  Pile detections: 888 (100.0%)\n", "  Non-pile areas: 0 (0.0%)\n", "  Average pile probability: 0.9998\n", "  High confidence piles (>0.9): 888\n", "\n", "Ending Cell 24-----------------------------------------\n", "Executing Cell 25--------------------------------------\n", "   grid_index            x             y  pile_probability prediction\n", "0           1  385721.4523  3.529185e+06          0.999791       PILE\n", "1           4  385721.4523  3.529200e+06          0.999788       PILE\n", "2           7  385721.4523  3.529215e+06          0.999787       PILE\n", "3          55  385726.4523  3.529185e+06          0.999803       PILE\n", "4          56  385726.4523  3.529190e+06          0.999809       PILE\n", "\n", "Ending Cell 25-----------------------------------------\n", "Executing Cell 26--------------------------------------\n", "Point Cloud Bounds (X, Y): [ 385723.9523 3529182.8306] [ 385809.6253 3529447.0086]\n", "\n", "Ending Cell 26-----------------------------------------\n", "Executing Cell 27--------------------------------------\n", "Ending Cell 27-----------------------------------------\n", "Executing Cell 28--------------------------------------\n", "Pile X Range: 385721.4522999996 385811.4522999996\n", "Pile Y Range: 3529180.330600001 3529445.330600001\n", "PointCloud X Range: 385723.9522999996 385809.6252999996\n", "PointCloud Y Range: 3529182.830600001 3529447.0086000008\n", "\n", "Ending Cell 28-----------------------------------------\n", "Executing Cell 29--------------------------------------\n", "Saved pile visualization to: output_runs/pointnet_plus_plus_inference/nortan_res/nortan_res_pile_visualization.png\n", "\n", "<Figure size 1500x600 with 3 Axes>\n", "Ending Cell 29-----------------------------------------\n", "Executing Cell 30--------------------------------------\n", "<Figure size 1000x800 with 2 Axes>\n", "Ending Cell 30-----------------------------------------\n", "Executing Cell 31--------------------------------------\n", "Results exported to: output_runs/pointnet_plus_plus_inference/nortan_res/nortan_res_pile_detections_20250728_202947.csv\n", "Summary statistics saved to: output_runs/pointnet_plus_plus_inference/nortan_res/nortan_res_analysis_summary_20250728_202947.json\n", "\n", "Ending Cell 31-----------------------------------------\n", "Executing Cell 32--------------------------------------\n", "Ending Cell 32-----------------------------------------\n", "Executing Cell 33--------------------------------------\n", "DWG file not available for comparison\n", "\n", "Ending Cell 33-----------------------------------------\n", "Executing Cell 34--------------------------------------\n", "\n", "==================================================\n", "SITE ANALYSIS COMPLETE\n", "==================================================\n", "Site: nortan_res\n", "Analysis completed at: 2025-07-28 20:29:47\n", "Model loaded: True\n", "Point cloud loaded: True\n", "Key findings:\n", "  - Analyzed 888 locations\n", "  - Found 888 potential pile locations\n", "  - Detection confidence: 1.000 average\n", "  - Results exported to output directory\n", "MLflow run completed\n", "Output directory: output_runs/pointnet_plus_plus_inference/nortan_res\n", "\n", "Ending Cell 34-----------------------------------------\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✓ Completed: nortan_res\n", "\n", "Total execution time: 2:49:08.161390\n"]}], "source": ["if validation_issues:\n", "    print(\"Skipping execution due to validation errors.\")\n", "    results = []\n", "else:\n", "    print(\"Starting multi-site inference...\\n\" + \"=\" * 50)\n", "    start_time = datetime.now()\n", "    \n", "    results = [\n", "        {\n", "            \"site\": cfg[\"site_name\"],\n", "            \"description\": cfg[\"description\"],\n", "            \"success\": (res := run_site_inference(cfg))[0],\n", "            \"error\": res[1],\n", "            \"output_notebook\": f\"02_pointnet_plus_plus_inference_{cfg['site_name']}_executed.ipynb\"\n", "        }\n", "        for i, cfg in enumerate(SITE_CONFIGS, 1)\n", "    ]\n", "    \n", "    print(f\"\\nTotal execution time: {datetime.now() - start_time}\")\n"]}, {"cell_type": "code", "execution_count": 11, "id": "summary_report", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "============================================================\n", "EXECUTION SUMMARY\n", "============================================================\n", "Total sites: 2 | Success: 1 | Failed: 1\n", "\n", "althea_rpcs: FAILED\n", "    Error: Papermill failed for althea_rpcs\n", "nortan_res: SUCCESS\n", "\n", "Generated Notebooks:\n", "============================================================\n", "  02_pointnet_plus_plus_inference_althea_rpcs_executed.ipynb (0.1 MB)\n", "  02_pointnet_plus_plus_inference_nortan_res_executed.ipynb (0.8 MB)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>site</th>\n", "      <th>description</th>\n", "      <th>status</th>\n", "      <th>output_notebook</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>althea_rpcs</td>\n", "      <td>Althea RPCS site with Point_Cloud.las</td>\n", "      <td>FAILED</td>\n", "      <td>02_pointnet_plus_plus_inference_althea_rpcs_ex...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>nortan_res</td>\n", "      <td>Nortan RES site with Block_11_2m.las</td>\n", "      <td>SUCCESS</td>\n", "      <td>02_pointnet_plus_plus_inference_nortan_res_exe...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          site                            description   status  \\\n", "0  althea_rpcs  Althea RPCS site with Point_Cloud.las   FAILED   \n", "1   nortan_res   Nortan RES site with Block_11_2m.las  SUCCESS   \n", "\n", "                                     output_notebook  \n", "0  02_pointnet_plus_plus_inference_althea_rpcs_ex...  \n", "1  02_pointnet_plus_plus_inference_nortan_res_exe...  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Completed! Results saved to: multi_site_inference_results.csv\n"]}], "source": ["if results:\n", "    print(\"\\n\" + \"=\"*60)\n", "    print(\"EXECUTION SUMMARY\")\n", "    print(\"=\"*60)\n", "\n", "    success_count = sum(r[\"success\"] for r in results)\n", "    fail_count = len(results) - success_count\n", "\n", "    print(f\"Total sites: {len(results)} | Success: {success_count} | Failed: {fail_count}\\n\")\n", "\n", "    for r in results:\n", "        status = \"SUCCESS\" if r[\"success\"] else f\"FAILED\\n    Error: {r['error']}\"\n", "        print(f\"{r['site']}: {status}\")\n", "\n", "    print(\"\\nGenerated Notebooks:\")\n", "    print(\"=\"*60)\n", "    for r in results:\n", "        nb_path = Path(r[\"output_notebook\"])\n", "        size = f\"{nb_path.stat().st_size / (1024 * 1024):.1f} MB\" if nb_path.exists() else \"not found\"\n", "        print(f\"  {nb_path.name} ({size})\")\n", "\n", "    # Display and save results\n", "    df = pd.DataFrame(results)\n", "    df[\"status\"] = df[\"success\"].map({True: \"SUCCESS\", False: \"FAILED\"})\n", "    display(df[[\"site\", \"description\", \"status\", \"output_notebook\"]])\n", "    \n", "    csv_path = \"multi_site_inference_results.csv\"\n", "    df.to_csv(csv_path, index=False)\n", "    print(f\"\\nCompleted! Results saved to: {csv_path}\")\n", "else:\n", "    print(\"No results to display due to validation or execution failure.\")\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}