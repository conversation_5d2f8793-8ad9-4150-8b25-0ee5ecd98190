{"cells": [{"cell_type": "markdown", "metadata": {"id": "uwcbcYyM5alm"}, "source": ["# PointNet++ <PERSON>le Detection\n", "\n", "This notebook implements PointNet++ architecture for pile detection using the patch data prepared from the successful harmonization and extraction pipeline.\n", "\n", "\n", "**Architecture:**\n", "- Set abstraction layers for hierarchical feature learning\n", "- Multi-scale grouping for robust feature extraction\n", "- Feature propagation for dense prediction\n", "- Binary classification (pile vs non-pile)\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "code", "source": ["from google.colab import drive\n", "drive.mount('/content/drive')\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "oKrohjNO6YP9", "outputId": "4db58c19-46d7-467d-f761-d3f504eb2d57"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Drive already mounted at /content/drive; to attempt to forcibly remount, call drive.mount(\"/content/drive\", force_remount=True).\n"]}]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "9CEYy2EX5aln", "outputId": "6395246b-f9ef-46f5-cf6f-2bcd774e4371"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["=== POINTNET++ PILE DETECTION ===\n", "Goal: Beat rule-based baseline F1 of 0.932\n", "Goal: Beat rule-based baseline accuracy of 0.885\n"]}], "source": ["import sys\n", "from pathlib import Path\n", "import pickle\n", "import json\n", "\n", "# Parameters\n", "batch_size = 16\n", "num_epochs = 50\n", "learning_rate = 0.001\n", "num_points = 256 #512  # Reduced from 1024 to match your data size\n", "save_model = True\n", "\n", "\n", "# Baseline performance to beat (from your rule-based classifier)\n", "BASELINE_F1 = 0.932  # 93.2% F1-score from rule-based\n", "BASELINE_ACCURACY = 0.885  # 88.5% accuracy\n", "\n", "print(\"=== POINTNET++ PILE DETECTION ===\")\n", "print(f\"Goal: Beat rule-based baseline F1 of {BASELINE_F1:.3f}\")\n", "print(f\"Goal: Beat rule-based baseline accuracy of {BASELINE_ACCURACY:.3f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "yChKecXP5aln", "outputId": "ff4e4f2c-deb1-425e-8dc8-5e711092857a"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Using device: cuda\n"]}], "source": ["import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "from torch.utils.data import Dataset, DataLoader\n", "import numpy as np\n", "from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix\n", "import matplotlib.pyplot as plt\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "#device = torch.device('mps' if torch.backends.mps.is_available() else 'cpu')\n", "\n", "print(f\"Using device: {device}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "7O065U-t5aln", "outputId": "f04c0089-7ca6-422c-b104-0aadeac3f58e"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["ls: cannot access '../../00_data_preprocessing/output/ml_patch_data/patches_20250722_160244': No such file or directory\n"]}], "source": ["!ls -lh ../../00_data_preprocessing/output/ml_patch_data/patches_20250722_160244"]}, {"cell_type": "code", "source": ["!pwd"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "d1LhCVzr67Hk", "outputId": "1f15676b-ec45-4d5f-85cf-024359ae917e"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["/content\n"]}]}, {"cell_type": "code", "source": ["!ls -lh /content/drive/MyDrive/'Colab Notebooks'/data\n", "!ls -lh /content/drive/MyDrive/'Colab Notebooks'/data/patches_20250722_160244"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "8o4R78ml7BKm", "outputId": "652d97a2-4ca4-4038-e052-1cae824e8503"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["total 4.0K\n", "drwx------ 2 <USER> <GROUP> 4.0K Jul 22 16:13 patches_20250722_160244\n", "total 9.0M\n", "-rw------- 1 <USER> <GROUP>  415 Jul 22 10:32 config.json\n", "-rw------- 1 <USER> <GROUP> 2.7K Jul 22 11:03 enhanced_classification_metrics.json\n", "-rw------- 1 <USER> <GROUP> 1.6M Jul 22 11:51 feature_analysis_results.csv\n", "-rw------- 1 <USER> <GROUP> 232K Jul 22 11:51 feature_analysis_test.csv\n", "-rw------- 1 <USER> <GROUP> 1.1M Jul 22 11:51 feature_analysis_train.csv\n", "-rw------- 1 <USER> <GROUP> 231K Jul 22 11:51 feature_analysis_val.csv\n", "-rw------- 1 <USER> <GROUP> 1.2K Jul 22 11:51 feature_comparison.csv\n", "-rw------- 1 <USER> <GROUP> 207K Jul 22 10:32 test_metadata.json\n", "-rw------- 1 <USER> <GROUP> 699K Jul 22 10:32 test_patches.pkl\n", "-rw------- 1 <USER> <GROUP> 962K Jul 22 10:32 train_metadata.json\n", "-rw------- 1 <USER> <GROUP> 3.2M Jul 22 10:32 train_patches.pkl\n", "-rw------- 1 <USER> <GROUP> 207K Jul 22 10:32 val_metadata.json\n", "-rw------- 1 <USER> <GROUP> 705K Jul 22 10:32 val_patches.pkl\n"]}]}, {"cell_type": "markdown", "metadata": {"id": "E2F5qbC-5aln"}, "source": ["## Data Loading"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "J1LQok965aln"}, "outputs": [], "source": ["import pickle\n", "import json\n", "from pathlib import Path\n", "\n", "\n", "#def load_preprocessed_patches(base_dir=\"../../00_data_preprocessing/output/ml_patch_data\"):\n", "def load_preprocessed_patches(base_dir=\"/content/drive/MyDrive/Colab Notebooks/data\"):\n", "\n", "    \"\"\"\n", "    Load pre-extracted and labeled patch datasets from the most recent patch folder.\n", "    Assumes the structure:\n", "      ../../00_data_preprocessing/output/ml_patch_data/patches_<timestamp>/{train,val,test}_patches.pkl\n", "    \"\"\"\n", "    try:\n", "        patch_root = Path(base_dir).resolve()\n", "        if not patch_root.exists():\n", "            raise FileNotFoundError(f\"Patch root not found: {patch_root}\")\n", "\n", "        # Auto-detect latest patch directory\n", "        latest_patch_dir = sorted(patch_root.glob(\"patches_*\"))[-1]\n", "        print(f\"Using latest patch data from: {latest_patch_dir}\")\n", "\n", "        datasets = {}\n", "        for split in ['train', 'val', 'test']:\n", "            patch_file = latest_patch_dir / f\"{split}_patches.pkl\"\n", "            meta_file = latest_patch_dir / f\"{split}_metadata.json\"\n", "\n", "            if not patch_file.exists() or not meta_file.exists():\n", "                raise FileNotFoundError(f\"Missing files for '{split}':\\n  - {patch_file}\\n  - {meta_file}\")\n", "\n", "            datasets[split] = {\n", "                'patches': pickle.load(open(patch_file, 'rb')),\n", "                'metadata': json.load(open(meta_file))\n", "            }\n", "            print(f\"  {split.capitalize()}: {len(datasets[split]['patches'])} patches\")\n", "\n", "        return datasets\n", "\n", "    except Exception as e:\n", "        print(f\"Failed to load patch data: {e}\")\n", "        raise\n", "\n", "\n", "def sample_patch_to_fixed_size(patch, target_n=512, noise_scale=0.01):\n", "    \"\"\"Randomly sample or pad a point patch to a fixed number of points\"\"\"\n", "    patch = np.array(patch, dtype=np.float32)\n", "    n = len(patch)\n", "\n", "    if n >= target_n:\n", "        return patch[np.random.choice(n, target_n, replace=False)]\n", "\n", "    # Pad with jittered copies of existing points\n", "    extra = np.stack([\n", "        patch[np.random.randint(n)] + np.random.normal(0, noise_scale, 3)\n", "        for _ in range(target_n - n)\n", "    ])\n", "    return np.vstack([patch, extra])\n", "\n", "\n", "def preprocess_patches_for_pointnet(patches, metadata, num_points=256):\n", "    \"\"\"Convert patches and metadata into PointNet++-ready format\"\"\"\n", "    out_patches, out_labels = [], []\n", "\n", "    for patch, meta in zip(patches, metadata):\n", "        if len(patch) < 10:\n", "            continue  # Ignore too-small patches\n", "\n", "        patch_fixed = sample_patch_to_fixed_size(patch, num_points)\n", "        patch_fixed /= np.max(np.linalg.norm(patch_fixed, axis=1)) or 1  # Normalize\n", "\n", "        out_patches.append(patch_fixed)\n", "        out_labels.append(int(meta.get('label', meta.get('patch_type') == 'positive')))\n", "\n", "    return np.array(out_patches, dtype=np.float32), np.array(out_labels, dtype=np.int64)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "k383XElV5alo", "outputId": "58261c11-1d15-411b-a6fc-877810d5c7fe"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Using latest patch data from: /content/drive/MyDrive/Colab Notebooks/data/patches_20250722_160244\n", "  Train: 3345 patches\n", "  Val: 717 patches\n", "  Test: 717 patches\n", "Formatting patches for PointNet++...\n", "\n", "Final shapes:\n", "Train: <PERSON><PERSON> (3345, 256, 3), <PERSON>s (3345,)\n", "Val: <PERSON><PERSON> (717, 256, 3), <PERSON><PERSON> (717,)\n", "Test: <PERSON><PERSON> (717, 256, 3), <PERSON><PERSON> (717,)\n", "\n", "Class distribution:\n", "  Train: 2684 positive, 661 negative (80.2% positive)\n", "  Val: 576 positive, 141 negative (80.3% positive)\n", "  Test: 575 positive, 142 negative (80.2% positive)\n"]}], "source": ["datasets = load_preprocessed_patches()\n", "print(\"Formatting patches for PointNet++...\")\n", "\n", "train_patches, train_labels = preprocess_patches_for_pointnet(**datasets['train'])\n", "val_patches, val_labels = preprocess_patches_for_pointnet(**datasets['val'])\n", "test_patches, test_labels = preprocess_patches_for_pointnet(**datasets['test'])\n", "\n", "print(f\"\\nFinal shapes:\")\n", "for split, data in zip(['<PERSON>', 'Val', 'Test'],\n", "                       [(train_patches, train_labels), (val_patches, val_labels), (test_patches, test_labels)]):\n", "    print(f\"{split}: Patches {data[0].shape}, Labels {data[1].shape}\")\n", "\n", "# Check class distribution\n", "print(f\"\\nClass distribution:\")\n", "for split, labels in [('Train', train_labels), ('Val', val_labels), ('Test', test_labels)]:\n", "    pos_count = np.sum(labels)\n", "    neg_count = len(labels) - pos_count\n", "    print(f\"  {split}: {pos_count} positive, {neg_count} negative ({pos_count/len(labels)*100:.1f}% positive)\")\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 857}, "id": "Ubnzuc9l5alo", "outputId": "457bbd2f-bb1d-4239-a402-7ed19d3ae9a4"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 640x480 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 640x480 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}], "source": ["import matplotlib.pyplot as plt\n", "\n", "def plot_patch(patch, title):\n", "    fig = plt.figure()\n", "    ax = fig.add_subplot(111, projection='3d')\n", "    ax.scatter(patch[:, 0], patch[:, 1], patch[:, 2], s=1)\n", "    ax.set_title(title)\n", "    plt.show()\n", "\n", "# Show one positive and negative sample\n", "plot_patch(train_patches[train_labels == 1][0], \"Positive Patch\")\n", "plot_patch(train_patches[train_labels == 0][0], \"Negative Patch\")\n"]}, {"cell_type": "markdown", "metadata": {"id": "-CZQN30P5alo"}, "source": ["## Dataset and DataLoader"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "KXuZO9065alo", "outputId": "397b8c5a-ecb8-42c5-e690-3f6efd8c8bb3"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Train: 3345, Val: 717, Test: 717\n"]}], "source": ["from torch.utils.data import DataLoader, Dataset\n", "import torch\n", "\n", "class PileDataset(Dataset):\n", "    def __init__(self, points, labels):\n", "        self.points = torch.FloatTensor(points)\n", "        self.labels = torch.LongTensor(labels)\n", "\n", "    def __len__(self):\n", "        return len(self.points)\n", "\n", "    def __getitem__(self, idx):\n", "        return self.points[idx], self.labels[idx]\n", "\n", "# Create datasets\n", "train_dataset = PileDataset(train_patches, train_labels)\n", "val_dataset = PileDataset(val_patches, val_labels)\n", "test_dataset = PileDataset(test_patches, test_labels)\n", "\n", "# Create dataloaders\n", "batch_size = 32  # or whatever value you use\n", "#train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)\n", "train_loader = DataLoader(train_dataset, batch_size=16, shuffle=True, drop_last=True)\n", "\n", "val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)\n", "test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)\n", "\n", "print(f\"Train: {len(train_dataset)}, Val: {len(val_dataset)}, Test: {len(test_dataset)}\")"]}, {"cell_type": "markdown", "metadata": {"id": "bqYTve2-5alo"}, "source": ["## PointNet++ Architecture"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "bbAq5dny5alo"}, "outputs": [], "source": ["def square_distance(src, dst):\n", "    \"\"\"Calculate squared distance between points\"\"\"\n", "    B, N, _ = src.shape\n", "    _, M, _ = dst.shape\n", "    dist = -2 * torch.matmul(src, dst.permute(0, 2, 1))\n", "    dist += torch.sum(src ** 2, -1).view(B, N, 1)\n", "    dist += torch.sum(dst ** 2, -1).view(B, 1, M)\n", "    return dist\n", "\n", "def farthest_point_sample(xyz, npoint):\n", "    \"\"\"Farthest point sampling\"\"\"\n", "    device = xyz.device\n", "    B, N, C = xyz.shape\n", "    centroids = torch.zeros(B, npoint, dtype=torch.long).to(device)\n", "    distance = torch.ones(B, N).to(device) * 1e10\n", "    farthest = torch.randint(0, N, (B,), dtype=torch.long).to(device)\n", "    batch_indices = torch.arange(B, dtype=torch.long).to(device)\n", "\n", "    for i in range(npoint):\n", "        centroids[:, i] = farthest\n", "        centroid = xyz[batch_indices, farthest, :].view(B, 1, 3)\n", "        dist = torch.sum((xyz - centroid) ** 2, -1)\n", "        mask = dist < distance\n", "        distance[mask] = dist[mask]\n", "        farthest = torch.max(distance, -1)[1]\n", "\n", "    return centroids\n", "\n", "def query_ball_point(radius, nsample, xyz, new_xyz):\n", "    \"\"\"Ball query\"\"\"\n", "    device = xyz.device\n", "    B, N, C = xyz.shape\n", "    _, S, _ = new_xyz.shape\n", "    group_idx = torch.arange(N, dtype=torch.long).to(device).view(1, 1, N).repeat([B, S, 1])\n", "    sqrdists = square_distance(new_xyz, xyz)\n", "    group_idx[sqrdists > radius ** 2] = N\n", "    group_idx = group_idx.sort(dim=-1)[0][:, :, :nsample]\n", "    group_first = group_idx[:, :, 0].view(B, S, 1).repeat([1, 1, nsample])\n", "    mask = group_idx == N\n", "    group_idx[mask] = group_first[mask]\n", "    return group_idx\n", "\n", "class PointNetSetAbstraction(nn.Module):\n", "    def __init__(self, npoint, radius, nsample, in_channel, mlp, group_all):\n", "        super(PointNetSetAbstraction, self).__init__()\n", "        self.npoint = npoint\n", "        self.radius = radius\n", "        self.nsample = nsample\n", "        self.mlp_convs = nn.ModuleList()\n", "        self.mlp_bns = nn.ModuleList()\n", "        self.group_all = group_all\n", "\n", "        last_channel = in_channel\n", "        for out_channel in mlp:\n", "            self.mlp_convs.append(nn.Conv2d(last_channel, out_channel, 1))\n", "            self.mlp_bns.append(nn.BatchNorm2d(out_channel))\n", "            last_channel = out_channel\n", "\n", "    def forward(self, xyz, points):\n", "        B, N, C = xyz.shape\n", "\n", "        if self.group_all:\n", "            new_xyz = torch.zeros(B, 1, C).to(xyz.device)\n", "            new_points = points.view(B, 1, N, -1)\n", "        else:\n", "            fps_idx = farthest_point_sample(xyz, self.npoint)\n", "            new_xyz = xyz[torch.arange(B)[:, None], fps_idx]\n", "            idx = query_ball_point(self.radius, self.nsample, xyz, new_xyz)\n", "            grouped_xyz = xyz[torch.arange(B)[:, None, None], idx]\n", "            grouped_xyz_norm = grouped_xyz - new_xyz.view(B, self.npoint, 1, C)\n", "\n", "            if points is not None:\n", "                grouped_points = points[torch.arange(B)[:, None, None], idx]\n", "                new_points = torch.cat([grouped_xyz_norm, grouped_points], dim=-1)\n", "            else:\n", "                new_points = grouped_xyz_norm\n", "\n", "        new_points = new_points.permute(0, 3, 2, 1)\n", "        for i, conv in enumerate(self.mlp_convs):\n", "            bn = self.mlp_bns[i]\n", "            new_points = torch.relu(bn(conv(new_points)))\n", "\n", "        new_points = torch.max(new_points, 2)[0]\n", "        new_points = new_points.permute(0, 2, 1)\n", "\n", "        return new_xyz, new_points"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ABOzWGAi5alo", "outputId": "54d86135-7bf3-4e11-b6c6-3ccae25a401d"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Model initialized with 1,465,154 parameters\n"]}], "source": ["class PointNetPlusPlus(nn.Module):\n", "    def __init__(self, num_classes=2):\n", "        super(PointNetPlusPlus, self).__init__()\n", "\n", "        # Set abstraction layers\n", "        # self.sa1 = PointNetSetAbstraction(512, 0.2, 32, 3, [64, 64, 128], False)\n", "        # self.sa2 = PointNetSetAbstraction(128, 0.4, 64, 128 + 3, [128, 128, 256], False)\n", "        # self.sa3 = PointNetSetAbstraction(None, None, None, 256 + 3, [256, 512, 1024], True)\n", "\n", "        # self.sa1 = PointNetSetAbstraction(512, 0.2, 32, 3, [64, 64, 128], False)\n", "        # self.sa2 = PointNetSetAbstraction(128, 0.4, 64, 128, [128, 128, 256], False)\n", "        # self.sa3 = PointNetSetAbstraction(None, None, None, 256, [256, 512, 1024], True)\n", "\n", "        self.sa1 = PointNetSetAbstraction(512, 0.2, 32, 3, [64, 64, 128], False)           # 3\n", "        self.sa2 = PointNetSetAbstraction(128, 0.4, 64, 128 + 3, [128, 128, 256], False)   # 128+3=131\n", "        self.sa3 = PointNetSetAbstraction(None, None, None, 256, [256, 512, 1024], True)    # 256 only\n", "\n", "        # Classification head\n", "        self.fc1 = nn.Linear(1024, 512)\n", "        self.bn1 = nn.BatchNorm1d(512)\n", "        self.drop1 = nn.Dropout(0.4)\n", "        self.fc2 = nn.Linear(512, 256)\n", "        self.bn2 = nn.BatchNorm1d(256)\n", "        self.drop2 = nn.Dropout(0.4)\n", "        self.fc3 = nn.Linear(256, num_classes)\n", "\n", "    def forward(self, xyz):\n", "        if xyz.shape[1] == 3:\n", "            xyz = xyz.transpose(1, 2).contiguous()  # Ensure shape is (B, N, 3)\n", "\n", "        B, _, _ = xyz.shape\n", "\n", "        l1_xyz, l1_points = self.sa1(xyz, None)\n", "        l2_xyz, l2_points = self.sa2(l1_xyz, l1_points)\n", "        l3_xyz, l3_points = self.sa3(l2_xyz, l2_points)\n", "\n", "        x = l3_points.view(B, 1024)\n", "        x = self.drop1(torch.relu(self.bn1(self.fc1(x))))\n", "        x = self.drop2(torch.relu(self.bn2(self.fc2(x))))\n", "        x = self.fc3(x)\n", "\n", "        return x\n", "\n", "# Initialize model\n", "model = PointNetPlusPlus(num_classes=2).to(device)\n", "criterion = nn.CrossEntropyLoss()\n", "optimizer = optim.Adam(model.parameters(), lr=learning_rate)\n", "\n", "print(f\"Model initialized with {sum(p.numel() for p in model.parameters()):,} parameters\")"]}, {"cell_type": "markdown", "metadata": {"id": "tH5jMoiu5alo"}, "source": ["## Training"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "z4gYwAPX5alo"}, "outputs": [], "source": ["import torch\n", "from torch.cuda.amp import autocast, GradScaler\n", "\n", "use_amp = torch.cuda.is_available()\n", "scaler = GradScaler(enabled=use_amp)\n", "\n", "def train_epoch(model, loader, criterion, optimizer, device):\n", "    model.train()\n", "    total_loss = 0\n", "    correct = 0\n", "    total = 0\n", "\n", "    for batch_idx, (data, target) in enumerate(loader):\n", "        data, target = data.to(device), target.to(device)\n", "\n", "        optimizer.zero_grad()\n", "        with autocast(enabled=use_amp):  # Only use AMP on CUDA\n", "            output = model(data)\n", "            loss = criterion(output, target)\n", "\n", "        if use_amp:\n", "            scaler.scale(loss).backward()\n", "            scaler.step(optimizer)\n", "            scaler.update()\n", "        else:\n", "            loss.backward()\n", "            optimizer.step()\n", "\n", "        total_loss += loss.item()\n", "        pred = output.argmax(dim=1)\n", "        correct += pred.eq(target).sum().item()\n", "        total += target.size(0)\n", "\n", "    return total_loss / len(loader), correct / total\n", "\n", "def validate_epoch(model, loader, criterion, device):\n", "    model.eval()\n", "    total_loss = 0\n", "    correct = 0\n", "    total = 0\n", "\n", "    with torch.no_grad():\n", "        for data, target in loader:\n", "            data, target = data.to(device), target.to(device)\n", "            output = model(data)\n", "            loss = criterion(output, target)\n", "\n", "            total_loss += loss.item()\n", "            pred = output.argmax(dim=1)\n", "            correct += pred.eq(target).sum().item()\n", "            total += target.size(0)\n", "\n", "    return total_loss / len(loader), correct / total\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "oOjv-NDJ5alo", "outputId": "cb5db9e1-3324-42fa-e6df-fa3d63675cc5"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Starting training...\n"]}], "source": ["print(\"Starting training...\")\n", "\n", "train_losses = []\n", "val_losses = []\n", "train_accs = []\n", "val_accs = []\n", "\n", "best_val_acc = 0\n", "patience = 10\n", "patience_counter = 0\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "IhOj-TLb5alo", "outputId": "11c40934-cc0c-4fff-c80c-ffc8e7d5b5ab"}, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["PointNet++ Training:  20%|██        | 10/50 [08:37<34:14, 51.37s/it]"]}, {"output_type": "stream", "name": "stdout", "text": ["Epoch 10/50:\n", "  Train Loss: 0.2162, Train Acc: 0.9139\n", "  Val Loss: 0.2328, Val Acc: 0.9191\n"]}, {"output_type": "stream", "name": "stderr", "text": ["PointNet++ Training:  38%|███▊      | 19/50 [17:08<27:58, 54.14s/it]"]}, {"output_type": "stream", "name": "stdout", "text": ["Epoch 20/50:\n", "  Train Loss: 0.1878, Train Acc: 0.9300\n", "  Val Loss: 0.2579, Val Acc: 0.9135\n", "Early stopping at epoch 20\n", "Training completed. Best validation accuracy: 0.9191\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}], "source": ["from tqdm import tqdm\n", "\n", "pbar = tqdm(range(num_epochs), desc=\"PointNet++ Training\")\n", "\n", "for epoch in pbar:\n", "    train_loss, train_acc = train_epoch(model, train_loader, criterion, optimizer, device)\n", "    val_loss, val_acc = validate_epoch(model, val_loader, criterion, device)\n", "\n", "    train_losses.append(train_loss)\n", "    val_losses.append(val_loss)\n", "    train_accs.append(train_acc)\n", "    val_accs.append(val_acc)\n", "\n", "    if (epoch + 1) % 10 == 0:\n", "        print(f\"Epoch {epoch+1}/{num_epochs}:\")\n", "        print(f\"  Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.4f}\")\n", "        print(f\"  Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.4f}\")\n", "\n", "    # Early stopping\n", "    if val_acc > best_val_acc:\n", "        best_val_acc = val_acc\n", "        patience_counter = 0\n", "        if save_model:\n", "            torch.save(model.state_dict(), 'best_pointnet_plus_plus.pth')\n", "    else:\n", "        patience_counter += 1\n", "        if patience_counter >= patience:\n", "            print(f\"Early stopping at epoch {epoch+1}\")\n", "            break\n", "print(f\"Training completed. Best validation accuracy: {best_val_acc:.4f}\")"]}, {"cell_type": "markdown", "metadata": {"id": "P7Znbj0E5alo"}, "source": ["## Evaluation"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "lKuowIZk5alo", "outputId": "05e01a92-6aa2-4ce8-860e-a1d2da2bbfbb"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["=== POINTNET++ TEST RESULTS ===\n", "Accuracy: 0.9079\n", "Precision: 0.9321\n", "Recall: 0.9548\n", "F1-Score: 0.9433\n"]}], "source": ["# Load best model for evaluation\n", "if save_model:\n", "    model.load_state_dict(torch.load('best_pointnet_plus_plus.pth'))\n", "\n", "# Test evaluation\n", "model.eval()\n", "all_preds = []\n", "all_targets = []\n", "\n", "with torch.no_grad():\n", "    for data, target in test_loader:\n", "        data, target = data.to(device), target.to(device)\n", "        output = model(data)\n", "        pred = output.argmax(dim=1)\n", "\n", "        all_preds.extend(pred.cpu().numpy())\n", "        all_targets.extend(target.cpu().numpy())\n", "\n", "# Calculate metrics\n", "test_accuracy = accuracy_score(all_targets, all_preds)\n", "test_precision = precision_score(all_targets, all_preds)\n", "test_recall = recall_score(all_targets, all_preds)\n", "test_f1 = f1_score(all_targets, all_preds)\n", "\n", "print(\"=== POINTNET++ TEST RESULTS ===\")\n", "print(f\"Accuracy: {test_accuracy:.4f}\")\n", "print(f\"Precision: {test_precision:.4f}\")\n", "print(f\"Recall: {test_recall:.4f}\")\n", "print(f\"F1-Score: {test_f1:.4f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "APJaegye5alo", "outputId": "890bbe3a-222c-4e99-bd9b-24290a309282"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "=== COMPARISON WITH RULE-BASED BASELINE ===\n", "Rule-based F1: 0.9320\n", "PointNet++ F1: 0.9433\n", "Improvement: 1.2%\n"]}], "source": ["# Compare with rule-based baseline\n", "print(\"\\n=== COMPARISON WITH RULE-BASED BASELINE ===\")\n", "print(f\"Rule-based F1: {BASELINE_F1:.4f}\")\n", "print(f\"PointNet++ F1: {test_f1:.4f}\")\n", "improvement = ((test_f1 - BASELINE_F1) / BASELINE_F1) * 100\n", "print(f\"Improvement: {improvement:.1f}%\")\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "hXpPV7cC5alo", "outputId": "7abbd167-0d17-4a0a-a146-208708612e6c"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "Results saved to pointnet_plus_plus_results.json\n", "Ready for DGCNN comparison.\n"]}], "source": ["# Save results\n", "results = {\n", "    'model': 'PointNet++',\n", "    'test_metrics': {\n", "        'accuracy': float(test_accuracy),\n", "        'precision': float(test_precision),\n", "        'recall': float(test_recall),\n", "        'f1_score': float(test_f1)\n", "    },\n", "    'training_info': {\n", "        'num_epochs': len(train_losses),\n", "        'best_val_acc': float(best_val_acc),\n", "        'final_train_acc': float(train_accs[-1]),\n", "        'final_val_acc': float(val_accs[-1])\n", "    },\n", "    'comparison': {\n", "        'rule_based_f1': float(BASELINE_F1),\n", "        'pointnet_plus_plus_f1': float(test_f1),\n", "        'improvement_percent': float(improvement)\n", "    }\n", "}\n", "\n", "with open('pointnet_plus_plus_results.json', 'w') as f:\n", "    json.dump(results, f, indent=2)\n", "\n", "print(f\"\\nResults saved to pointnet_plus_plus_results.json\")\n", "print(\"Ready for DGCNN comparison.\")"]}, {"cell_type": "code", "source": ["import shutil\n", "from pathlib import Path\n", "\n", "# Destination path – replace with your actual drive mount path if using Colab\n", "dest_dir = Path(\"/content/drive/MyDrive/pointnet_results/\")\n", "dest_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "# Move model checkpoint\n", "shutil.move(\"best_pointnet_plus_plus.pth\", dest_dir / \"best_pointnet_plus_plus.pth\")\n", "\n", "# Move JSON results\n", "shutil.move(\"pointnet_plus_plus_results.json\", dest_dir / \"pointnet_plus_plus_results.json\")\n", "\n", "print(\"Model and results moved to Google Drive.\")\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "thYpWxET_thO", "outputId": "fa50ba21-61b4-4b9e-8382-879fe451e7b5"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Model and results moved to Google Drive.\n"]}]}], "metadata": {"kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}, "colab": {"provenance": [], "gpuType": "T4"}, "accelerator": "GPU"}, "nbformat": 4, "nbformat_minor": 0}