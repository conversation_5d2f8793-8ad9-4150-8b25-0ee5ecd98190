import sys
from pathlib import Path
import pickle
import json

# Parameters
batch_size = 4 #16
num_epochs = 5  # 100
learning_rate = 0.001
num_points = 128 #256 #512  # Reduced from 1024 to match your data size
save_model = True

# Baseline performance to beat (from your rule-based classifier)
BASELINE_F1 = 0.932  # 93.2% F1-score from rule-based
BASELINE_ACCURACY = 0.885  # 88.5% accuracy

print("=== POINTNET++ PILE DETECTION ===")
print(f"Goal: Beat rule-based baseline F1 of {BASELINE_F1:.3f}")
print(f"Goal: Beat rule-based baseline accuracy of {BASELINE_ACCURACY:.3f}")

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix
import matplotlib.pyplot as plt
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

#device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
device = torch.device('mps' if torch.backends.mps.is_available() else 'cpu')

print(f"Using device: {device}")


!ls -lh ../../00_data_preprocessing/output/ml_patch_data/patches_20250722_160244

import pickle
import json
from pathlib import Path


def load_preprocessed_patches(base_dir="../../00_data_preprocessing/output/ml_patch_data"):
    """
    Load pre-extracted and labeled patch datasets from the most recent patch folder.
    Assumes the structure:
      ../../00_data_preprocessing/output/ml_patch_data/patches_<timestamp>/{train,val,test}_patches.pkl
    """
    try:
        patch_root = Path(base_dir).resolve()
        if not patch_root.exists():
            raise FileNotFoundError(f"Patch root not found: {patch_root}")

        # Auto-detect latest patch directory
        latest_patch_dir = sorted(patch_root.glob("patches_*"))[-1]
        print(f"Using latest patch data from: {latest_patch_dir}")

        datasets = {}
        for split in ['train', 'val', 'test']:
            patch_file = latest_patch_dir / f"{split}_patches.pkl"
            meta_file = latest_patch_dir / f"{split}_metadata.json"

            if not patch_file.exists() or not meta_file.exists():
                raise FileNotFoundError(f"Missing files for '{split}':\n  - {patch_file}\n  - {meta_file}")

            datasets[split] = {
                'patches': pickle.load(open(patch_file, 'rb')),
                'metadata': json.load(open(meta_file))
            }
            print(f"  {split.capitalize()}: {len(datasets[split]['patches'])} patches")

        return datasets

    except Exception as e:
        print(f"Failed to load patch data: {e}")
        raise


def sample_patch_to_fixed_size(patch, target_n=512, noise_scale=0.01):
    """Randomly sample or pad a point patch to a fixed number of points"""
    patch = np.array(patch, dtype=np.float32)
    n = len(patch)

    if n >= target_n:
        return patch[np.random.choice(n, target_n, replace=False)]

    # Pad with jittered copies of existing points
    extra = np.stack([
        patch[np.random.randint(n)] + np.random.normal(0, noise_scale, 3)
        for _ in range(target_n - n)
    ])
    return np.vstack([patch, extra])


def preprocess_patches_for_pointnet(patches, metadata, num_points=128):
    """Convert patches and metadata into PointNet++-ready format"""
    out_patches, out_labels = [], []

    for patch, meta in zip(patches, metadata):
        if len(patch) < 10:
            continue  # Ignore too-small patches

        patch_fixed = sample_patch_to_fixed_size(patch, num_points)
        patch_fixed /= np.max(np.linalg.norm(patch_fixed, axis=1)) or 1  # Normalize

        out_patches.append(patch_fixed)
        out_labels.append(int(meta.get('label', meta.get('patch_type') == 'positive')))

    return np.array(out_patches, dtype=np.float32), np.array(out_labels, dtype=np.int64)

datasets = load_preprocessed_patches()
print("Formatting patches for PointNet++...")

train_patches, train_labels = preprocess_patches_for_pointnet(**datasets['train'])
val_patches, val_labels = preprocess_patches_for_pointnet(**datasets['val'])
test_patches, test_labels = preprocess_patches_for_pointnet(**datasets['test'])

print(f"\nFinal shapes:")
for split, data in zip(['Train', 'Val', 'Test'],
                       [(train_patches, train_labels), (val_patches, val_labels), (test_patches, test_labels)]):
    print(f"{split}: Patches {data[0].shape}, Labels {data[1].shape}")

# Check class distribution
print(f"\nClass distribution:")
for split, labels in [('Train', train_labels), ('Val', val_labels), ('Test', test_labels)]:
    pos_count = np.sum(labels)
    neg_count = len(labels) - pos_count
    print(f"  {split}: {pos_count} positive, {neg_count} negative ({pos_count/len(labels)*100:.1f}% positive)")



import matplotlib.pyplot as plt

def plot_patch(patch, title):
    fig = plt.figure()
    ax = fig.add_subplot(111, projection='3d')
    ax.scatter(patch[:, 0], patch[:, 1], patch[:, 2], s=1)
    ax.set_title(title)
    plt.show()

# Show one positive and negative sample
plot_patch(train_patches[train_labels == 1][0], "Positive Patch")
plot_patch(train_patches[train_labels == 0][0], "Negative Patch")


from torch.utils.data import DataLoader, Dataset
import torch

class PileDataset(Dataset):
    def __init__(self, points, labels):
        self.points = torch.FloatTensor(points)
        self.labels = torch.LongTensor(labels)
    
    def __len__(self):
        return len(self.points)
    
    def __getitem__(self, idx):
        return self.points[idx], self.labels[idx]

# Create datasets
train_dataset = PileDataset(train_patches, train_labels)
val_dataset = PileDataset(val_patches, val_labels)
test_dataset = PileDataset(test_patches, test_labels)

# Create dataloaders
batch_size = 16  # or whatever value you use
#train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
train_loader = DataLoader(train_dataset, batch_size=16, shuffle=True, drop_last=True)

val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)

print(f"Train: {len(train_dataset)}, Val: {len(val_dataset)}, Test: {len(test_dataset)}")

def square_distance(src, dst):
    """Calculate squared distance between points"""
    B, N, _ = src.shape
    _, M, _ = dst.shape
    dist = -2 * torch.matmul(src, dst.permute(0, 2, 1))
    dist += torch.sum(src ** 2, -1).view(B, N, 1)
    dist += torch.sum(dst ** 2, -1).view(B, 1, M)
    return dist

def farthest_point_sample(xyz, npoint):
    """Farthest point sampling"""
    device = xyz.device
    B, N, C = xyz.shape
    centroids = torch.zeros(B, npoint, dtype=torch.long).to(device)
    distance = torch.ones(B, N).to(device) * 1e10
    farthest = torch.randint(0, N, (B,), dtype=torch.long).to(device)
    batch_indices = torch.arange(B, dtype=torch.long).to(device)
    
    for i in range(npoint):
        centroids[:, i] = farthest
        centroid = xyz[batch_indices, farthest, :].view(B, 1, 3)
        dist = torch.sum((xyz - centroid) ** 2, -1)
        mask = dist < distance
        distance[mask] = dist[mask]
        farthest = torch.max(distance, -1)[1]
    
    return centroids

def query_ball_point(radius, nsample, xyz, new_xyz):
    """Ball query"""
    device = xyz.device
    B, N, C = xyz.shape
    _, S, _ = new_xyz.shape
    group_idx = torch.arange(N, dtype=torch.long).to(device).view(1, 1, N).repeat([B, S, 1])
    sqrdists = square_distance(new_xyz, xyz)
    group_idx[sqrdists > radius ** 2] = N
    group_idx = group_idx.sort(dim=-1)[0][:, :, :nsample]
    group_first = group_idx[:, :, 0].view(B, S, 1).repeat([1, 1, nsample])
    mask = group_idx == N
    group_idx[mask] = group_first[mask]
    return group_idx

class PointNetSetAbstraction(nn.Module):
    def __init__(self, npoint, radius, nsample, in_channel, mlp, group_all):
        super(PointNetSetAbstraction, self).__init__()
        self.npoint = npoint
        self.radius = radius
        self.nsample = nsample
        self.mlp_convs = nn.ModuleList()
        self.mlp_bns = nn.ModuleList()
        self.group_all = group_all
        
        last_channel = in_channel
        for out_channel in mlp:
            self.mlp_convs.append(nn.Conv2d(last_channel, out_channel, 1))
            self.mlp_bns.append(nn.BatchNorm2d(out_channel))
            last_channel = out_channel
    
    def forward(self, xyz, points):
        B, N, C = xyz.shape
        
        if self.group_all:
            new_xyz = torch.zeros(B, 1, C).to(xyz.device)
            new_points = points.view(B, 1, N, -1)
        else:
            fps_idx = farthest_point_sample(xyz, self.npoint)
            new_xyz = xyz[torch.arange(B)[:, None], fps_idx]
            idx = query_ball_point(self.radius, self.nsample, xyz, new_xyz)
            grouped_xyz = xyz[torch.arange(B)[:, None, None], idx]
            grouped_xyz_norm = grouped_xyz - new_xyz.view(B, self.npoint, 1, C)
            
            if points is not None:
                grouped_points = points[torch.arange(B)[:, None, None], idx]
                new_points = torch.cat([grouped_xyz_norm, grouped_points], dim=-1)
            else:
                new_points = grouped_xyz_norm
        
        new_points = new_points.permute(0, 3, 2, 1)
        for i, conv in enumerate(self.mlp_convs):
            bn = self.mlp_bns[i]
            new_points = torch.relu(bn(conv(new_points)))
        
        new_points = torch.max(new_points, 2)[0]
        new_points = new_points.permute(0, 2, 1)
        
        return new_xyz, new_points

class PointNetPlusPlus(nn.Module):
    def __init__(self, num_classes=2):
        super(PointNetPlusPlus, self).__init__()
        
        # Set abstraction layers
        # self.sa1 = PointNetSetAbstraction(512, 0.2, 32, 3, [64, 64, 128], False)
        # self.sa2 = PointNetSetAbstraction(128, 0.4, 64, 128 + 3, [128, 128, 256], False)
        # self.sa3 = PointNetSetAbstraction(None, None, None, 256 + 3, [256, 512, 1024], True)
        
        # self.sa1 = PointNetSetAbstraction(512, 0.2, 32, 3, [64, 64, 128], False)
        # self.sa2 = PointNetSetAbstraction(128, 0.4, 64, 128, [128, 128, 256], False)
        # self.sa3 = PointNetSetAbstraction(None, None, None, 256, [256, 512, 1024], True)

        self.sa1 = PointNetSetAbstraction(512, 0.2, 32, 3, [64, 64, 128], False)           # 3
        self.sa2 = PointNetSetAbstraction(128, 0.4, 64, 128 + 3, [128, 128, 256], False)   # 128+3=131
        self.sa3 = PointNetSetAbstraction(None, None, None, 256, [256, 512, 1024], True)    # 256 only

        # Classification head
        self.fc1 = nn.Linear(1024, 512)
        self.bn1 = nn.BatchNorm1d(512)
        self.drop1 = nn.Dropout(0.4)
        self.fc2 = nn.Linear(512, 256)
        self.bn2 = nn.BatchNorm1d(256)
        self.drop2 = nn.Dropout(0.4)
        self.fc3 = nn.Linear(256, num_classes)
    
    def forward(self, xyz):
        if xyz.shape[1] == 3:
            xyz = xyz.transpose(1, 2).contiguous()  # Ensure shape is (B, N, 3)

        B, _, _ = xyz.shape
        
        l1_xyz, l1_points = self.sa1(xyz, None)
        l2_xyz, l2_points = self.sa2(l1_xyz, l1_points)
        l3_xyz, l3_points = self.sa3(l2_xyz, l2_points)
        
        x = l3_points.view(B, 1024)
        x = self.drop1(torch.relu(self.bn1(self.fc1(x))))
        x = self.drop2(torch.relu(self.bn2(self.fc2(x))))
        x = self.fc3(x)
        
        return x

# Initialize model
model = PointNetPlusPlus(num_classes=2).to(device)
criterion = nn.CrossEntropyLoss()
optimizer = optim.Adam(model.parameters(), lr=learning_rate)

print(f"Model initialized with {sum(p.numel() for p in model.parameters()):,} parameters")

def train_epoch(model, loader, criterion, optimizer, device):
    model.train()
    total_loss = 0
    correct = 0
    total = 0

    for batch_idx, (data, target) in enumerate(loader):
        data, target = data.to(device), target.to(device)

        optimizer.zero_grad()
        output = model(data)
        loss = criterion(output, target)
        loss.backward()
        optimizer.step()

        total_loss += loss.item()
        pred = output.argmax(dim=1)
        correct += pred.eq(target).sum().item()
        total += target.size(0)

    return total_loss / len(loader), correct / total

def validate_epoch(model, loader, criterion, device):
    model.eval()
    total_loss = 0
    correct = 0
    total = 0

    with torch.no_grad():
        for data, target in loader:
            data, target = data.to(device), target.to(device)
            output = model(data)
            loss = criterion(output, target)

            total_loss += loss.item()
            pred = output.argmax(dim=1)
            correct += pred.eq(target).sum().item()
            total += target.size(0)

    return total_loss / len(loader), correct / total


print("Starting training...")

train_losses = []
val_losses = []
train_accs = []
val_accs = []

best_val_acc = 0
patience = 10
patience_counter = 0


from tqdm import tqdm

pbar = tqdm(range(num_epochs), desc="PointNet++ Training")

for epoch in pbar:
    train_loss, train_acc = train_epoch(model, train_loader, criterion, optimizer, device)
    val_loss, val_acc = validate_epoch(model, val_loader, criterion, device)

    train_losses.append(train_loss)
    val_losses.append(val_loss)
    train_accs.append(train_acc)
    val_accs.append(val_acc)

    if (epoch + 1) % 10 == 0:
        print(f"Epoch {epoch+1}/{num_epochs}:")
        print(f"  Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.4f}")
        print(f"  Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.4f}")

    # Early stopping
    if val_acc > best_val_acc:
        best_val_acc = val_acc
        patience_counter = 0
        if save_model:
            torch.save(model.state_dict(), 'best_pointnet_plus_plus.pth')
    else:
        patience_counter += 1
        if patience_counter >= patience:
            print(f"Early stopping at epoch {epoch+1}")
            break
print(f"Training completed. Best validation accuracy: {best_val_acc:.4f}")

# Check if MPS is actually working:
print(f"Device: {device}")
print(f"MPS available: {torch.backends.mps.is_available()}")
print(f"Model device: {next(model.parameters()).device}")

# Load best model for evaluation
if save_model:
    model.load_state_dict(torch.load('best_pointnet_plus_plus.pth'))

# Test evaluation
model.eval()
all_preds = []
all_targets = []

with torch.no_grad():
    for data, target in test_loader:
        data, target = data.to(device), target.to(device)
        output = model(data)
        pred = output.argmax(dim=1)
        
        all_preds.extend(pred.cpu().numpy())
        all_targets.extend(target.cpu().numpy())

# Calculate metrics
test_accuracy = accuracy_score(all_targets, all_preds)
test_precision = precision_score(all_targets, all_preds)
test_recall = recall_score(all_targets, all_preds)
test_f1 = f1_score(all_targets, all_preds)

print("=== POINTNET++ TEST RESULTS ===")
print(f"Accuracy: {test_accuracy:.4f}")
print(f"Precision: {test_precision:.4f}")
print(f"Recall: {test_recall:.4f}")
print(f"F1-Score: {test_f1:.4f}")

# Compare with rule-based baseline
print("\n=== COMPARISON WITH RULE-BASED BASELINE ===")
print(f"Rule-based F1: {BASELINE_F1:.4f}")
print(f"PointNet++ F1: {test_f1:.4f}")
improvement = ((test_f1 - BASELINE_F1) / BASELINE_F1) * 100
print(f"Improvement: {improvement:.1f}%")



# Save results
results = {
    'model': 'PointNet++',
    'test_metrics': {
        'accuracy': float(test_accuracy),
        'precision': float(test_precision),
        'recall': float(test_recall),
        'f1_score': float(test_f1)
    },
    'training_info': {
        'num_epochs': len(train_losses),
        'best_val_acc': float(best_val_acc),
        'final_train_acc': float(train_accs[-1]),
        'final_val_acc': float(val_accs[-1])
    },
    'comparison': {
        'rule_based_f1': float(BASELINE_F1),  
        'pointnet_plus_plus_f1': float(test_f1),
        'improvement_percent': float(improvement)
    }
}

with open('pointnet_plus_plus_results.json', 'w') as f:
    json.dump(results, f, indent=2)

print(f"\nResults saved to pointnet_plus_plus_results.json")
print("Ready for DGCNN comparison.")