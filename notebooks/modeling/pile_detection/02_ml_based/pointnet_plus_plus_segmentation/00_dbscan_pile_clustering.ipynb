{"cells": [{"cell_type": "markdown", "id": "9145b7a0", "metadata": {}, "source": ["# DBSCAN-Based Pile Clustering\n", "This notebook uses the **Density-Based Spatial Clustering of Applications with Noise (DBSCAN)** algorithm to cluster potential **pile locations** from a cleaned point cloud.\n", "\n", "This step serves as a **preprocessing stage** to:\n", "1. Reduce noise\n", "2. Group spatially connected structures\n", "3. Isolate candidate regions for downstream PointNet/ML model\n", "\n", " ---\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025  \n", "**Project**: As-Built Foundation Analysis\n"]}, {"cell_type": "code", "execution_count": 1, "id": "b26014f2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["total 266624\n", "-rw-r--r--@ 1 <USER>  <GROUP>   130M Sep 20  2024 Croped_pointcloud_FLy4_Giorgio.las\n"]}], "source": ["!ls -lh ../../../../data/raw/piani_di_giorgio/pointcloud/"]}, {"cell_type": "code", "execution_count": 2, "id": "92d9e787", "metadata": {}, "outputs": [], "source": ["# Parameters\n", "#POINT_CLOUD_PATH = \"../../../../data/processed/trino_enel/ground_segmentation/ransac_pmf/trino_enel_nonground.ply\"\n", "\n", "#POINT_CLOUD_PATH = \"../../../../data/raw/piani_di_giorgio/pointcloud/Croped_pointcloud_FLy4_Giorgio.las\"\n", "\n", "POINT_CLOUD_PATH = \"../../../../data/raw/nortan_res/pointcloud/Block_11_2m.las\"\n", "OUTPUT_DIR = \"dbscan_clusters\"\n", "\n", "# DBSCAN parameters\n", "DBSCAN_EPS = 2.5  # max distance (meters) between points in a cluster\n", "DBSCAN_MIN_SAMPLES = 8  # minimum points to form a cluster\n", "\n", "# Output options\n", "SAVE_INDIVIDUAL_CLUSTERS = True\n", "SAVE_CENTROIDS = True\n", "VISUALIZE_RESULTS = True\n"]}, {"cell_type": "code", "execution_count": 3, "id": "a7b63b0e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== PILE DISCOVERY PIPELINE (NO METADATA) ===\n", "Target: Discover all pile structures on site\n", "DBSCAN eps: 2.5m, min_samples: 8\n", "Output directory: dbscan_clusters\n"]}], "source": ["print(\"=== PILE DISCOVERY PIPELINE (NO METADATA) ===\")\n", "print(f\"Target: Discover all pile structures on site\")\n", "print(f\"DBSCAN eps: {DBSCAN_EPS}m, min_samples: {DBSCAN_MIN_SAMPLES}\")\n", "print(f\"Output directory: {OUTPUT_DIR}\")"]}, {"cell_type": "code", "execution_count": 4, "id": "474b3528", "metadata": {}, "outputs": [], "source": ["import open3d as o3d\n", "import numpy as np\n", "import pandas as pd\n", "from pathlib import Path\n", "import os\n", "from datetime import datetime\n", "import json\n", "import matplotlib.pyplot as plt\n", "from collections import defaultdict\n", "from sklearn.cluster import DBSCAN\n", "import warnings\n", "warnings.filterwarnings('ignore')\n"]}, {"cell_type": "markdown", "id": "2f4fb83a", "metadata": {}, "source": ["## Step 1: Load Point Cloud\n"]}, {"cell_type": "code", "execution_count": 5, "id": "371f5774", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["total 0\n", "drwxr-xr-x@  5 <USER>  <GROUP>   160B Jul  5 21:31 \u001b[34madvanced_ifc_metadata\u001b[m\u001b[m\n", "drwxr-xr-x@  6 <USER>  <GROUP>   192B Jul  9 13:03 \u001b[34maligned_coordinates\u001b[m\u001b[m\n", "drwxr-xr-x@  5 <USER>  <GROUP>   160B Jul 23 12:09 \u001b[34malignment_visualizations\u001b[m\u001b[m\n", "drwxr-xr-x@  2 <USER>  <GROUP>    64B Jul  4 17:11 \u001b[34manalysis\u001b[m\u001b[m\n", "drwxr-xr-x@  3 <USER>  <GROUP>    96B Jul 21 07:17 \u001b[34mcoordinate_alignment\u001b[m\u001b[m\n", "drwxr-xr-x@  8 <USER>  <GROUP>   256B Jul 23 14:18 \u001b[34mcorrectionnet_comparison\u001b[m\u001b[m\n", "drwxr-xr-x@  7 <USER>  <GROUP>   224B Jul 23 14:38 \u001b[34mcorrectionnet_tuning\u001b[m\u001b[m\n", "drwxr-xr-x@ 17 <USER>  <GROUP>   544B Jul 20 17:12 \u001b[34mdenoising\u001b[m\u001b[m\n", "drwxr-xr-x@  4 <USER>  <GROUP>   128B Jul 21 12:43 \u001b[34mgcp_alignment\u001b[m\u001b[m\n", "drwxr-xr-x@  4 <USER>  <GROUP>   128B Jul 21 13:03 \u001b[34mgcp_alignment_z_corrected\u001b[m\u001b[m\n", "drwxr-xr-x@  6 <USER>  <GROUP>   192B Jul  4 15:07 \u001b[34mground_segmentation\u001b[m\u001b[m\n", "drwxr-xr-x@  6 <USER>  <GROUP>   192B Jul 14 19:11 \u001b[34mifc_metadata\u001b[m\u001b[m\n", "drwxr-xr-x@ 10 <USER>  <GROUP>   320B Jul 16 22:12 \u001b[34mifc_pointclouds\u001b[m\u001b[m\n", "drwxr-xr-x@  5 <USER>  <GROUP>   160B Jul 16 13:20 \u001b[34mifc_pointclouds_corrected\u001b[m\u001b[m\n", "drwxr-xr-x@  6 <USER>  <GROUP>   192B Jul 23 14:41 \u001b[34mlocal_nonuniform_alignment\u001b[m\u001b[m\n", "drwxr-xr-x@  8 <USER>  <GROUP>   256B Jul 23 15:15 \u001b[34mml_local_alignment\u001b[m\u001b[m\n", "drwxr-xr-x@  3 <USER>  <GROUP>    96B Jul 14 15:40 \u001b[34mvalidation\u001b[m\u001b[m\n", "drwxr-xr-x@  3 <USER>  <GROUP>    96B Jul 18 13:00 \u001b[34mz_deviation\u001b[m\u001b[m\n"]}], "source": ["!ls -lh ../../../../data/processed/trino_enel"]}, {"cell_type": "code", "execution_count": 6, "id": "79826635", "metadata": {}, "outputs": [], "source": ["import os\n", "import laspy\n", "import numpy as np\n", "import open3d as o3d\n", "\n", "def load_point_cloud(file_path):\n", "    ext = os.path.splitext(file_path)[1].lower()\n", "    \n", "    if ext == '.las':\n", "        las = laspy.read(file_path)\n", "        points = np.vstack((las.x, las.y, las.z)).T\n", "        pcd = o3d.geometry.PointCloud()\n", "        pcd.points = o3d.utility.Vector3dVector(points)\n", "    elif ext == '.ply':\n", "        pcd = o3d.io.read_point_cloud(file_path)\n", "    else:\n", "        raise ValueError(f\"Unsupported file extension: {ext}\")\n", "    \n", "    return pcd\n", "\n", "pcd = load_point_cloud(POINT_CLOUD_PATH)\n", "o3d.visualization.draw_geometries([pcd])\n"]}, {"cell_type": "markdown", "id": "0ca79b50", "metadata": {}, "source": ["## Step 2: <PERSON><PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": 7, "id": "ddc08a8c", "metadata": {}, "outputs": [], "source": ["pcd, ind = pcd.remove_statistical_outlier(nb_neighbors=20, std_ratio=2.0)\n", "points = np.asarray(pcd.points)"]}, {"cell_type": "markdown", "id": "8f111131", "metadata": {}, "source": ["## Step 3: DBSCAN Clustering"]}, {"cell_type": "code", "execution_count": 8, "id": "3e66b2fb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running DBSCAN on 27512 points\n", "Found 368 clusters\n"]}], "source": ["import numpy as np\n", "from sklearn.cluster import DBSCAN\n", "import open3d as o3d\n", "\n", "# Downsample the cloud\n", "pcd_down = pcd.voxel_down_sample(voxel_size=0.5)\n", "points = np.asarray(pcd_down.points)\n", "\n", "print(f\"Running DBSCAN on {points.shape[0]} points\")\n", "\n", "# Clustering\n", "eps = 2.5\n", "min_samples = 10\n", "clustering = DBSCAN(eps=eps, min_samples=min_samples).fit(points)\n", "labels = clustering.labels_\n", "n_clusters = len(set(labels)) - (1 if -1 in labels else 0)\n", "\n", "print(f\"Found {n_clusters} clusters\")\n"]}, {"cell_type": "markdown", "id": "0a16d738", "metadata": {}, "source": ["## Step 4: Visualize Clusters"]}, {"cell_type": "code", "execution_count": 9, "id": "c25da7ee", "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import open3d as o3d\n", "import numpy as np\n", "\n", "def visualize_clusters(points, labels):\n", "    # Create Open3D point cloud\n", "    pcd = o3d.geometry.PointCloud()\n", "    pcd.points = o3d.utility.Vector3dVector(points)\n", "\n", "    # Colormap: RGB only, skip alpha\n", "    cmap = plt.get_cmap(\"tab20\")\n", "    colors = [\n", "        cmap(label % 20)[:3] if label != -1 else (0, 0, 0)  # strip alpha\n", "        for label in labels\n", "    ]\n", "\n", "    pcd.colors = o3d.utility.Vector3dVector(colors)\n", "    o3d.visualization.draw_geometries([pcd])\n", "\n", "\n", "from collections import Counter\n", "\n", "def filter_top_clusters(points, labels, top_k=200):\n", "    cluster_sizes = Counter(labels)\n", "    top_labels = set(\n", "        label for label, count in cluster_sizes.most_common(top_k) if label != -1\n", "    )\n", "    mask = np.isin(labels, list(top_labels))\n", "    return points[mask], labels[mask]\n", "\n", "filtered_points, filtered_labels = filter_top_clusters(points, labels, top_k=200)\n", "\n", "visualize_clusters(filtered_points, filtered_labels)\n"]}, {"cell_type": "markdown", "id": "65745ca4", "metadata": {}, "source": ["## Step 5: Save Cluster Results\n"]}, {"cell_type": "code", "execution_count": 10, "id": "b6b0b794", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Extracted 368 pile centroids\n"]}], "source": ["from collections import defaultdict\n", "\n", "clusters = defaultdict(list)\n", "\n", "# Group points by cluster label\n", "for point, label in zip(points, labels):\n", "    if label == -1:\n", "        continue\n", "    clusters[label].append(point)\n", "\n", "centroids = []\n", "for cluster_id, pts in clusters.items():\n", "    pts = np.array(pts)\n", "    center = pts.mean(axis=0)\n", "    centroids.append(center)\n", "\n", "centroids = np.array(centroids)\n", "print(f\"Extracted {len(centroids)} pile centroids\")\n"]}, {"cell_type": "code", "execution_count": 11, "id": "f332313c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Cluster point clouds and centroids saved.\n"]}], "source": ["import os\n", "\n", "os.makedirs(\"dbscan_clusters\", exist_ok=True)\n", "\n", "# Save each cluster as separate PCD/PLY\n", "for cluster_id, pts in clusters.items():\n", "    cluster_pcd = o3d.geometry.PointCloud()\n", "    cluster_pcd.points = o3d.utility.Vector3dVector(np.array(pts))\n", "    o3d.io.write_point_cloud(f\"dbscan_clusters/cluster_{cluster_id:03d}.ply\", cluster_pcd)\n", "\n", "# Save centroids\n", "np.savetxt(\"pile_centroids.csv\", centroids, delimiter=\",\", header=\"x,y,z\", comments=\"\")\n", "print(\"Cluster point clouds and centroids saved.\")\n"]}, {"cell_type": "code", "execution_count": 10, "id": "8e476958", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading point cloud...\n", "Running DBSCAN clustering...\n", "Found 368 clusters\n", "Saving clusters and labeled outputs...\n", "All outputs saved.\n"]}], "source": ["import os\n", "import numpy as np\n", "import pandas as pd\n", "import open3d as o3d\n", "import laspy\n", "from pathlib import Path\n", "from sklearn.cluster import DBSCAN\n", "from collections import defaultdict, Counter\n", "import matplotlib.pyplot as plt\n", "\n", "# === CONFIGURATION ===\n", "POINT_CLOUD_PATH = \"../../../../data/raw/nortan_res/pointcloud/Block_11_2m.las\"\n", "OUTPUT_DIR = \"dbscan_clusters\"\n", "LABELED_DIR = \"labeled_clusters\"\n", "\n", "DBSCAN_EPS = 2.5\n", "DBSCAN_MIN_SAMPLES = 8\n", "VOXEL_DOWNSAMPLE = 0.5\n", "\n", "# === STEP 1: LOAD POINT CLOUD ===\n", "def load_point_cloud(file_path):\n", "    ext = os.path.splitext(file_path)[1].lower()\n", "    if ext == '.las':\n", "        las = laspy.read(file_path)\n", "        points = np.vstack((las.x, las.y, las.z)).T\n", "        pcd = o3d.geometry.PointCloud()\n", "        pcd.points = o3d.utility.Vector3dVector(points)\n", "    elif ext == '.ply':\n", "        pcd = o3d.io.read_point_cloud(file_path)\n", "    else:\n", "        raise ValueError(f\"Unsupported file extension: {ext}\")\n", "    return pcd\n", "\n", "print(\"Loading point cloud...\")\n", "pcd = load_point_cloud(POINT_CLOUD_PATH)\n", "pcd, _ = pcd.remove_statistical_outlier(nb_neighbors=20, std_ratio=2.0)\n", "pcd = pcd.voxel_down_sample(voxel_size=VOXEL_DOWNSAMPLE)\n", "points = np.asarray(pcd.points)\n", "\n", "# === STEP 2: CLUSTERING ===\n", "print(\"Running DBSCAN clustering...\")\n", "clustering = DBSCAN(eps=DBSCAN_EPS, min_samples=DBSCAN_MIN_SAMPLES).fit(points)\n", "labels = clustering.labels_\n", "n_clusters = len(set(labels)) - (1 if -1 in labels else 0)\n", "print(f\"Found {n_clusters} clusters\")\n", "\n", "# === STEP 3: GROUP POINTS BY CLUSTER ===\n", "clusters = defaultdict(list)\n", "for point, label in zip(points, labels):\n", "    if label != -1:\n", "        clusters[label].append(point)\n", "\n", "# === STEP 4: AUTO-LABEL POINTS IN EACH CLUSTER ===\n", "def label_cluster_points(points, center, height_thresh=0.3, radius_thresh=0.6):\n", "    points = np.array(points)\n", "    dists = np.linalg.norm(points[:, :2] - center[:2], axis=1)\n", "    heights = points[:, 2]\n", "    z_ground = np.percentile(heights, 5)\n", "\n", "    labels = []\n", "    for i in range(len(points)):\n", "        dist_ok = dists[i] < radius_thresh\n", "        height_ok = heights[i] > (z_ground + height_thresh)\n", "        if dist_ok and height_ok:\n", "            labels.append(1)  # pile\n", "        elif heights[i] < (z_ground + 0.1):\n", "            labels.append(0)  # ground\n", "        else:\n", "            labels.append(2)  # noise\n", "    return labels\n", "\n", "os.makedirs(OUTPUT_DIR, exist_ok=True)\n", "os.makedirs(LABELED_DIR, exist_ok=True)\n", "centroids = []\n", "all_metadata = []\n", "\n", "print(\"Saving clusters and labeled outputs...\")\n", "for cluster_id, pts in clusters.items():\n", "    pts = np.array(pts)\n", "    center = pts.mean(axis=0)\n", "    centroids.append(center)\n", "    labels = label_cluster_points(pts, center)\n", "\n", "    # Color coding\n", "    label_colors = {\n", "        0: [0.4, 0.4, 0.4],  # ground = gray\n", "        1: [0, 1, 0],        # pile = green\n", "        2: [1, 0, 0],        # noise = red\n", "    }\n", "    colors = np.array([label_colors[l] for l in labels])\n", "\n", "    # Save colored PCD\n", "    cluster_pcd = o3d.geometry.PointCloud()\n", "    cluster_pcd.points = o3d.utility.Vector3dVector(pts)\n", "    cluster_pcd.colors = o3d.utility.Vector3dVector(colors)\n", "    o3d.io.write_point_cloud(f\"{LABELED_DIR}/cluster_{cluster_id:03d}.ply\", cluster_pcd)\n", "\n", "    # Save labeled .npz for ML\n", "    np.savez(f\"{LABELED_DIR}/cluster_{cluster_id:03d}.npz\", points=pts, labels=np.array(labels))\n", "\n", "    all_metadata.append({\n", "        \"cluster_id\": cluster_id,\n", "        \"num_points\": len(pts),\n", "        \"num_pile\": int(np.sum(np.array(labels) == 1)),\n", "        \"num_ground\": int(np.sum(np.array(labels) == 0)),\n", "        \"num_noise\": int(np.sum(np.array(labels) == 2))\n", "    })\n", "\n", "# Save centroids and metadata\n", "np.savetxt(\"pile_centroids.csv\", np.array(centroids), delimiter=\",\", header=\"x,y,z\", comments=\"\")\n", "pd.DataFrame(all_metadata).to_csv(f\"{LABELED_DIR}/label_summary.csv\", index=False)\n", "print(\"All outputs saved.\")\n"]}, {"cell_type": "code", "execution_count": 23, "id": "a2914676", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading point cloud...\n", "\u001b[1;33m[Open3D WARNING] Read geometry::PointCloud failed: unknown file extension for ../../../../data/raw/nortan_res/pointcloud/Block_11_2m.las (format: auto).\u001b[0;m\n", "Loaded 0 points\n", "Clustering...\n"]}, {"ename": "ValueError", "evalue": "Found array with 0 sample(s) (shape=(0, 3)) while a minimum of 1 is required by DBSCAN.", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[23]\u001b[39m\u001b[32m, line 87\u001b[39m\n\u001b[32m     84\u001b[39m OUTPUT_DIR = \u001b[33m\"\u001b[39m\u001b[33mdbscan_clusters\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m     85\u001b[39m LABELED_DIR = \u001b[33m\"\u001b[39m\u001b[33mlabeled_clusters\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m---> \u001b[39m\u001b[32m87\u001b[39m \u001b[43mrun_pipeline\u001b[49m\u001b[43m(\u001b[49m\u001b[43mPOINT_CLOUD_PATH\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mLABELED_DIR\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[23]\u001b[39m\u001b[32m, line 62\u001b[39m, in \u001b[36mrun_pipeline\u001b[39m\u001b[34m(pcd_path, output_dir, eps, min_samples, save)\u001b[39m\n\u001b[32m     59\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mLoaded \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mlen\u001b[39m(points)\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m points\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m     61\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33mClustering...\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m---> \u001b[39m\u001b[32m62\u001b[39m labels = \u001b[43mcluster_dbscan\u001b[49m\u001b[43m(\u001b[49m\u001b[43mpoints\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43meps\u001b[49m\u001b[43m=\u001b[49m\u001b[43meps\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmin_samples\u001b[49m\u001b[43m=\u001b[49m\u001b[43mmin_samples\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     63\u001b[39m clusters = extract_clusters(points, labels)\n\u001b[32m     64\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mFound \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mlen\u001b[39m(clusters)\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m clusters\u001b[39m\u001b[33m\"\u001b[39m)\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[23]\u001b[39m\u001b[32m, line 13\u001b[39m, in \u001b[36mcluster_dbscan\u001b[39m\u001b[34m(points, eps, min_samples)\u001b[39m\n\u001b[32m     12\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mcluster_dbscan\u001b[39m(points, eps=\u001b[32m0.5\u001b[39m, min_samples=\u001b[32m10\u001b[39m):\n\u001b[32m---> \u001b[39m\u001b[32m13\u001b[39m     clustering = \u001b[43mDBSCAN\u001b[49m\u001b[43m(\u001b[49m\u001b[43meps\u001b[49m\u001b[43m=\u001b[49m\u001b[43meps\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmin_samples\u001b[49m\u001b[43m=\u001b[49m\u001b[43mmin_samples\u001b[49m\u001b[43m)\u001b[49m\u001b[43m.\u001b[49m\u001b[43mfit\u001b[49m\u001b[43m(\u001b[49m\u001b[43mpoints\u001b[49m\u001b[43m[\u001b[49m\u001b[43m:\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m:\u001b[49m\u001b[32;43m3\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     14\u001b[39m     labels = clustering.labels_\n\u001b[32m     15\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m labels\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.local/lib/python3.11/site-packages/sklearn/base.py:1363\u001b[39m, in \u001b[36m_fit_context.<locals>.decorator.<locals>.wrapper\u001b[39m\u001b[34m(estimator, *args, **kwargs)\u001b[39m\n\u001b[32m   1356\u001b[39m     estimator._validate_params()\n\u001b[32m   1358\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m config_context(\n\u001b[32m   1359\u001b[39m     skip_parameter_validation=(\n\u001b[32m   1360\u001b[39m         prefer_skip_nested_validation \u001b[38;5;129;01mor\u001b[39;00m global_skip_validation\n\u001b[32m   1361\u001b[39m     )\n\u001b[32m   1362\u001b[39m ):\n\u001b[32m-> \u001b[39m\u001b[32m1363\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfit_method\u001b[49m\u001b[43m(\u001b[49m\u001b[43mestimator\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.local/lib/python3.11/site-packages/sklearn/cluster/_dbscan.py:394\u001b[39m, in \u001b[36mDBSCAN.fit\u001b[39m\u001b[34m(self, X, y, sample_weight)\u001b[39m\n\u001b[32m    365\u001b[39m \u001b[38;5;129m@_fit_context\u001b[39m(\n\u001b[32m    366\u001b[39m     \u001b[38;5;66;03m# DBSCAN.metric is not validated yet\u001b[39;00m\n\u001b[32m    367\u001b[39m     prefer_skip_nested_validation=\u001b[38;5;28;01mFalse\u001b[39;00m\n\u001b[32m    368\u001b[39m )\n\u001b[32m    369\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mfit\u001b[39m(\u001b[38;5;28mself\u001b[39m, X, y=\u001b[38;5;28;01mNone\u001b[39;00m, sample_weight=\u001b[38;5;28;01mNone\u001b[39;00m):\n\u001b[32m    370\u001b[39m \u001b[38;5;250m    \u001b[39m\u001b[33;03m\"\"\"Perform DBSCAN clustering from features, or distance matrix.\u001b[39;00m\n\u001b[32m    371\u001b[39m \n\u001b[32m    372\u001b[39m \u001b[33;03m    Parameters\u001b[39;00m\n\u001b[32m   (...)\u001b[39m\u001b[32m    392\u001b[39m \u001b[33;03m        Returns a fitted instance of self.\u001b[39;00m\n\u001b[32m    393\u001b[39m \u001b[33;03m    \"\"\"\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m394\u001b[39m     X = \u001b[43mvalidate_data\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mX\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43maccept_sparse\u001b[49m\u001b[43m=\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mcsr\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[32m    396\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m sample_weight \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m    397\u001b[39m         sample_weight = _check_sample_weight(sample_weight, X)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.local/lib/python3.11/site-packages/sklearn/utils/validation.py:2954\u001b[39m, in \u001b[36mvalidate_data\u001b[39m\u001b[34m(_estimator, X, y, reset, validate_separately, skip_check_array, **check_params)\u001b[39m\n\u001b[32m   2952\u001b[39m         out = X, y\n\u001b[32m   2953\u001b[39m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m no_val_X \u001b[38;5;129;01mand\u001b[39;00m no_val_y:\n\u001b[32m-> \u001b[39m\u001b[32m2954\u001b[39m     out = \u001b[43mcheck_array\u001b[49m\u001b[43m(\u001b[49m\u001b[43mX\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43minput_name\u001b[49m\u001b[43m=\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mX\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mcheck_params\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   2955\u001b[39m \u001b[38;5;28;01melif\u001b[39;00m no_val_X \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m no_val_y:\n\u001b[32m   2956\u001b[39m     out = _check_y(y, **check_params)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.local/lib/python3.11/site-packages/sklearn/utils/validation.py:1128\u001b[39m, in \u001b[36mcheck_array\u001b[39m\u001b[34m(array, accept_sparse, accept_large_sparse, dtype, order, copy, force_writeable, force_all_finite, ensure_all_finite, ensure_non_negative, ensure_2d, allow_nd, ensure_min_samples, ensure_min_features, estimator, input_name)\u001b[39m\n\u001b[32m   1126\u001b[39m     n_samples = _num_samples(array)\n\u001b[32m   1127\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m n_samples < ensure_min_samples:\n\u001b[32m-> \u001b[39m\u001b[32m1128\u001b[39m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[32m   1129\u001b[39m             \u001b[33m\"\u001b[39m\u001b[33mFound array with \u001b[39m\u001b[38;5;132;01m%d\u001b[39;00m\u001b[33m sample(s) (shape=\u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[33m) while a\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m   1130\u001b[39m             \u001b[33m\"\u001b[39m\u001b[33m minimum of \u001b[39m\u001b[38;5;132;01m%d\u001b[39;00m\u001b[33m is required\u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[33m.\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m   1131\u001b[39m             % (n_samples, array.shape, ensure_min_samples, context)\n\u001b[32m   1132\u001b[39m         )\n\u001b[32m   1134\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m ensure_min_features > \u001b[32m0\u001b[39m \u001b[38;5;129;01mand\u001b[39;00m array.ndim == \u001b[32m2\u001b[39m:\n\u001b[32m   1135\u001b[39m     n_features = array.shape[\u001b[32m1\u001b[39m]\n", "\u001b[31mValueError\u001b[39m: Found array with 0 sample(s) (shape=(0, 3)) while a minimum of 1 is required by DBSCAN."]}], "source": ["import numpy as np\n", "import open3d as o3d\n", "from sklearn.cluster import DBSCAN\n", "import os\n", "import json\n", "from collections import Counter\n", "\n", "def load_point_cloud(path):\n", "    pcd = o3d.io.read_point_cloud(path)\n", "    return np.asarray(pcd.points)\n", "\n", "def cluster_dbscan(points, eps=0.5, min_samples=10):\n", "    clustering = DBSCAN(eps=eps, min_samples=min_samples).fit(points[:, :3])\n", "    labels = clustering.labels_\n", "    return labels\n", "\n", "def extract_clusters(points, labels):\n", "    clusters = {}\n", "    for i, label in enumerate(labels):\n", "        if label == -1:\n", "            continue\n", "        clusters.setdefault(label, []).append(points[i])\n", "    return clusters\n", "\n", "def get_bounding_box_stats(cluster_points):\n", "    cluster_points = np.array(cluster_points)\n", "    min_bounds = np.min(cluster_points, axis=0)\n", "    max_bounds = np.max(cluster_points, axis=0)\n", "    dims = max_bounds - min_bounds\n", "    footprint = dims[0] * dims[1]\n", "    height = dims[2]\n", "    return dims, footprint, height\n", "\n", "def classify_cluster(cluster_points, height_thresh=0.3):\n", "    cluster_points = np.array(cluster_points)\n", "    dims, footprint, height = get_bounding_box_stats(cluster_points)\n", "\n", "    # Heuristic rules\n", "    if height > 0.6 and footprint < 1.0 and 0.4 < dims[0] < 1.2 and 0.4 < dims[1] < 1.2:\n", "        return \"pile\"\n", "    elif height < 0.4 and footprint > 1.5:\n", "        return \"pallet\"\n", "    elif footprint > 0.8 and dims[0] / dims[1] > 2.0:\n", "        return \"machinery\"\n", "    else:\n", "        return \"noise\"\n", "\n", "def save_cluster_ply(cluster_points, label, cluster_id, out_dir):\n", "    cluster_pcd = o3d.geometry.PointCloud()\n", "    cluster_pcd.points = o3d.utility.Vector3dVector(np.array(cluster_points))\n", "    filename = os.path.join(out_dir, f\"{label}_cluster_{cluster_id:03d}.ply\")\n", "    o3d.io.write_point_cloud(filename, cluster_pcd)\n", "\n", "def run_pipeline(pcd_path, output_dir=\"labeled_clusters\", eps=0.5, min_samples=10, save=True):\n", "    os.makedirs(output_dir, exist_ok=True)\n", "\n", "    print(\"Loading point cloud...\")\n", "    points = load_point_cloud(pcd_path)\n", "    print(f\"Loaded {len(points)} points\")\n", "\n", "    print(\"Clustering...\")\n", "    labels = cluster_dbscan(points, eps=eps, min_samples=min_samples)\n", "    clusters = extract_clusters(points, labels)\n", "    print(f\"Found {len(clusters)} clusters\")\n", "\n", "    label_summary = {}\n", "\n", "    for cid, cluster_points in clusters.items():\n", "        label = classify_cluster(cluster_points)\n", "        label_summary[cid] = label\n", "        print(f\"Cluster {cid}: {label} ({len(cluster_points)} pts)\")\n", "        if save:\n", "            save_cluster_ply(cluster_points, label, cid, output_dir)\n", "\n", "    with open(os.path.join(output_dir, \"cluster_labels.json\"), \"w\") as f:\n", "        json.dump(label_summary, f, indent=2)\n", "\n", "    print(\"Done.\")\n", "    print(\"Label counts:\", dict(Counter(label_summary.values())))\n", "\n", "# Example usage\n", "if __name__ == \"__main__\":\n", "    POINT_CLOUD_PATH = \"../../../../data/raw/nortan_res/pointcloud/Block_11_2m.las\"\n", "    OUTPUT_DIR = \"dbscan_clusters\"\n", "    LABELED_DIR = \"labeled_clusters\"\n", "\n", "    run_pipeline(POINT_CLOUD_PATH, LABELED_DIR)\n"]}, {"cell_type": "code", "execution_count": null, "id": "cf9d3e64", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Reading LAS file: ../../../../data/raw/nortan_res/pointcloud/Block_11_2m.las\n", "Loaded and downsampled to 75578 points\n", "Processing 75578 points\n", "Found 368 clusters\n", "\n", "Saved 368 clusters with labels to: labeled_clusters\n", "Label distribution: {'pallet': 35, 'noise': 304, 'machinery': 29}\n"]}], "source": ["import os\n", "import numpy as np\n", "import open3d as o3d\n", "from sklearn.cluster import DBSCAN\n", "import matplotlib.pyplot as plt\n", "import laspy\n", "import json\n", "\n", "def load_point_cloud_las(file_path, voxel_size=0.3):\n", "    print(f\"Reading LAS file: {file_path}\")\n", "    las = laspy.read(file_path)\n", "    coords = np.vstack((las.x, las.y, las.z)).T\n", "\n", "    # Downsample using voxel grid\n", "    pcd = o3d.geometry.PointCloud()\n", "    pcd.points = o3d.utility.Vector3dVector(coords)\n", "\n", "    if voxel_size:\n", "        pcd = pcd.voxel_down_sample(voxel_size=voxel_size)\n", "        coords = np.asarray(pcd.points)\n", "\n", "    print(f\"Loaded and downsampled to {len(coords)} points\")\n", "    return coords, pcd\n", "\n", "def cluster_dbscan(points, eps=0.5, min_samples=10):\n", "    return DBSCAN(eps=eps, min_samples=min_samples).fit(points).labels_\n", "\n", "def extract_clusters(points, labels):\n", "    clusters = {}\n", "    for idx in np.unique(labels):\n", "        if idx == -1:\n", "            continue  # noise\n", "        clusters[idx] = points[labels == idx]\n", "    return clusters\n", "\n", "def heuristic_label_cluster(cluster):\n", "    x_range = np.ptp(cluster[:, 0])\n", "    y_range = np.ptp(cluster[:, 1])\n", "    z_range = np.ptp(cluster[:, 2])\n", "\n", "    if z_range > x_range and z_range > y_range:\n", "        return \"pile\"\n", "    elif x_range > 1.5 and y_range > 1.5 and z_range < 0.8:\n", "        return \"pallet\"\n", "    elif x_range > 1.0 and z_range > 1.5:\n", "        return \"machinery\"\n", "    else:\n", "        return \"noise\"\n", "\n", "def visualize_labeled_clusters(clusters, labels_map):\n", "    colors = {\n", "        \"pile\": [1, 0, 0],\n", "        \"pallet\": [0, 1, 0],\n", "        \"machinery\": [0, 0, 1],\n", "        \"noise\": [0.5, 0.5, 0.5]\n", "    }\n", "    all_points = []\n", "    all_colors = []\n", "    for cid, pts in clusters.items():\n", "        label = labels_map[cid]\n", "        color = np.tile(colors[label], (pts.shape[0], 1))\n", "        all_points.append(pts)\n", "        all_colors.append(color)\n", "\n", "    merged_pcd = o3d.geometry.PointCloud()\n", "    merged_pcd.points = o3d.utility.Vector3dVector(np.vstack(all_points))\n", "    merged_pcd.colors = o3d.utility.Vector3dVector(np.vstack(all_colors))\n", "    o3d.visualization.draw_geometries([merged_pcd])\n", "\n", "def save_labeled_clusters(clusters, labels_map, save_dir=\"labeled_clusters\"):\n", "    os.makedirs(save_dir, exist_ok=True)\n", "\n", "    label_summary = {}\n", "    for cluster_id, points in clusters.items():\n", "        label = labels_map.get(cluster_id, \"unknown\")\n", "        cluster_name = f\"cluster_{cluster_id:03d}_{label}\"\n", "        \n", "        # Save cluster as PLY\n", "        ply_path = os.path.join(save_dir, f\"{cluster_name}.ply\")\n", "        pcd = o3d.geometry.PointCloud()\n", "        pcd.points = o3d.utility.Vector3dVector(points)\n", "        o3d.io.write_point_cloud(ply_path, pcd)\n", "\n", "        # Save label as a .npy file (all points in cluster get the same class)\n", "        npy_path = os.path.join(save_dir, f\"{cluster_name}.npy\")\n", "        class_id = {\n", "            \"pile\": 0,\n", "            \"support\": 1,\n", "            \"pallet\": 2,\n", "            \"machinery\": 3,\n", "            \"noise\": 4\n", "        }.get(label, 4)  # default to 'noise' if unknown\n", "\n", "        labels_array = np.full((points.shape[0],), class_id, dtype=np.int64)\n", "        np.save(npy_path, labels_array)\n", "\n", "        # Track summary\n", "        if label not in label_summary:\n", "            label_summary[label] = []\n", "        label_summary[label].append(cluster_name)\n", "\n", "    # Save summary to JSON\n", "    summary_path = os.path.join(save_dir, \"label_summary.json\")\n", "    with open(summary_path, \"w\") as f:\n", "        json.dump(label_summary, f, indent=4)\n", "\n", "    print(f\"\\nSaved {len(clusters)} clusters with labels to: {save_dir}\")\n", "    print(f\"Label distribution: { {k: len(v) for k, v in label_summary.items()} }\")\n", "\n", "def run_pipeline(pcd_path):\n", "    points, _ = load_point_cloud_las(pcd_path, voxel_size=0.3)  # Tune voxel_size\n", "    print(f\"Processing {len(points)} points\")\n", "\n", "    labels = cluster_dbscan(points, eps=0.8, min_samples=20)\n", "    clusters = extract_clusters(points, labels)\n", "    print(f\"Found {len(clusters)} clusters\")\n", "\n", "    # Apply heuristic labeling per cluster\n", "    labels_map = {}\n", "    for cid, cluster_points in clusters.items():\n", "        label = heuristic_label_cluster(cluster_points)\n", "        labels_map[cid] = label\n", "\n", "\n", "    save_labeled_clusters(clusters, labels_map, LABELED_DIR)\n", "    return clusters, labels_map\n", "# Example usage:\n", "POINT_CLOUD_PATH = \"../../../../data/raw/nortan_res/pointcloud/Block_11_2m.las\"\n", "OUTPUT_DIR = \"dbscan_clusters\"\n", "LABELED_DIR = \"labeled_clusters\"\n", "clusters, labels_map = run_pipeline(POINT_CLOUD_PATH)\n", "\n"]}, {"cell_type": "code", "execution_count": 4, "id": "ecfac907", "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import open3d as o3d\n", "import random\n", "\n", "def visualize_clusters_by_label(clusters, labels_map, label, num_samples=5):\n", "    matching = [cid for cid, lbl in labels_map.items() if lbl == label]\n", "    sample_ids = random.sample(matching, min(len(matching), num_samples))\n", "\n", "    for cid in sample_ids:\n", "        pcd = o3d.geometry.PointCloud()\n", "        pcd.points = o3d.utility.Vector3dVector(clusters[cid])\n", "        o3d.visualization.draw_geometries([pcd], window_name=f\"{label} - Cluster {cid}\")\n", "\n", "# Visualize samples\n", "#visualize_clusters_by_label(clusters, labels_map, \"pallet\", num_samples=5)\n", "#visualize_clusters_by_label(clusters, labels_map, \"machinery\", num_samples=3)\n", "visualize_clusters_by_label(clusters, labels_map, \"pile\", num_samples=5)"]}, {"cell_type": "code", "execution_count": 5, "id": "ff4bd020", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Saved visualization screenshot to cluster_visualization.png\n"]}], "source": ["def visualize_clusters_by_label(clusters, labels_map, save_path=None):\n", "    import open3d as o3d\n", "    import random\n", "\n", "    label_colors = {\n", "        \"pallet\": [1.0, 0.0, 0.0],      # Red\n", "        \"machinery\": [0.0, 1.0, 0.0],   # Green\n", "        \"noise\": [0.5, 0.5, 0.5],       # <PERSON>\n", "    }\n", "\n", "    vis_list = []\n", "    for cluster_id, pts in clusters.items():\n", "        label = labels_map.get(cluster_id, \"noise\")\n", "        color = label_colors.get(label, [random.random(), random.random(), random.random()])\n", "        pcd = o3d.geometry.PointCloud()\n", "        pcd.points = o3d.utility.Vector3dVector(pts)\n", "        pcd.paint_uniform_color(color)\n", "        vis_list.append(pcd)\n", "\n", "    if save_path:\n", "        vis = o3d.visualization.Visualizer()\n", "        vis.create_window(visible=False)\n", "        for pcd in vis_list:\n", "            vis.add_geometry(pcd)\n", "        vis.poll_events()\n", "        vis.update_renderer()\n", "        vis.capture_screen_image(save_path)\n", "        vis.destroy_window()\n", "        print(f\"Saved visualization screenshot to {save_path}\")\n", "    else:\n", "        o3d.visualization.draw_geometries(vis_list)\n", "\n", "visualize_clusters_by_label(clusters, labels_map, \"cluster_visualization.png\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}