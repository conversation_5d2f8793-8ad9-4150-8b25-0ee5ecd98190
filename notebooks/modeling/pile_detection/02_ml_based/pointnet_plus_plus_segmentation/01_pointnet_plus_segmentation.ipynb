{"cells": [{"cell_type": "code", "execution_count": 1, "id": "2edc6539", "metadata": {}, "outputs": [], "source": ["import os\n", "import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "import torch.optim as optim\n", "from torch.utils.data import Dataset, DataLoader\n", "import numpy as np\n", "import open3d as o3d\n", "from pathlib import Path\n", "import json\n", "import matplotlib.pyplot as plt\n", "from sklearn.metrics import confusion_matrix, classification_report\n", "import seaborn as sns\n", "from collections import Counter\n"]}, {"cell_type": "code", "execution_count": 2, "id": "6bb33f7b", "metadata": {}, "outputs": [], "source": ["class PilePointCloudDataset(Dataset):\n", "    def __init__(self, base_names, data_dir=\"labeled_clusters\", num_points=128, num_classes=3, label_map=None):\n", "        self.data_dir = Path(data_dir)\n", "        self.base_names = base_names\n", "        self.num_points = num_points\n", "        self.num_classes = num_classes\n", "        self.label_map = label_map or {}\n", "\n", "    def __len__(self):\n", "        return len(self.base_names)\n", "\n", "    def __getitem__(self, idx):\n", "        base_name = self.base_names[idx]\n", "        ply_file = self.data_dir / f\"{base_name}.ply\"\n", "        npy_file = self.data_dir / f\"{base_name}.npy\"\n", "\n", "        # Load point cloud\n", "        pcd = o3d.io.read_point_cloud(str(ply_file))\n", "        points = np.asarray(pcd.points)\n", "\n", "        # Load labels\n", "        labels = np.load(npy_file)\n", "        \n", "        # Apply label remapping\n", "        if self.label_map:\n", "            remapped_labels = np.zeros_like(labels)\n", "            for old_label, new_label in self.label_map.items():\n", "                remapped_labels[labels == old_label] = new_label\n", "            labels = remapped_labels\n", "        \n", "        # Sanity check\n", "        if len(points) != len(labels):\n", "            raise ValueError(f\"Mismatch: {len(points)} points vs {len(labels)} labels in {base_name}\")\n", "\n", "        # Sample or pad points with different random state each time\n", "        np.random.seed(None)  # Ensure different sampling each epoch\n", "        if len(points) < self.num_points:\n", "            indices = np.random.choice(len(points), self.num_points, replace=True)\n", "        else:\n", "            indices = np.random.choice(len(points), self.num_points, replace=False)\n", "\n", "        sampled_points = points[indices]\n", "        sampled_labels = labels[indices]\n", "\n", "        # Clip label values to avoid out-of-bound errors\n", "        sampled_labels = np.clip(sampled_labels, 0, self.num_classes - 1)\n", "\n", "        return torch.tensor(sampled_points, dtype=torch.float32), torch.tensor(sampled_labels, dtype=torch.long)\n"]}, {"cell_type": "code", "execution_count": 3, "id": "76eb6354", "metadata": {}, "outputs": [], "source": ["class PointNetPlusPlusPointWise(nn.Module):\n", "    def __init__(self, num_classes):\n", "        super().__init__()\n", "        \n", "        self.mlp1 = nn.Sequential(\n", "            nn.<PERSON><PERSON>(3, 64), nn.<PERSON><PERSON>(),\n", "            nn.<PERSON><PERSON>(64, 64), nn.<PERSON><PERSON><PERSON>()\n", "        )\n", "        self.mlp2 = nn.Sequential(\n", "            nn.<PERSON><PERSON>(64, 128), nn.<PERSON><PERSON>(),\n", "            nn.<PERSON><PERSON>(128, 256), nn.<PERSON><PERSON><PERSON>()\n", "        )\n", "        \n", "        self.classifier = nn.Sequential(\n", "            nn.<PERSON><PERSON>(256, 256), nn.<PERSON><PERSON><PERSON>(),\n", "            nn.Dropout(0.3),  # Add dropout to prevent overfitting\n", "            nn.Linear(256, num_classes)\n", "        )\n", "\n", "    def forward(self, x):\n", "        # Input: [B, N, 3]\n", "        x = self.mlp1(x)\n", "        x = self.mlp2(x)\n", "        out = self.classifier(x)  # [B, N, num_classes]\n", "        return out\n", "\n"]}, {"cell_type": "code", "execution_count": 4, "id": "12b8912c", "metadata": {}, "outputs": [], "source": ["def train(model, dataloader, optimizer, criterion, device):\n", "    model.train()\n", "    total_loss = 0\n", "    num_batches = 0\n", "\n", "    for points, labels in dataloader:\n", "        # Move data to device\n", "        points = points.to(device)\n", "        labels = labels.to(device)\n", "        \n", "        optimizer.zero_grad()\n", "        outputs = model(points)\n", "        outputs = outputs.view(-1, outputs.shape[-1])  # [B*N, C]\n", "        labels = labels.view(-1)  # [B*N]\n", "        loss = criterion(outputs, labels)\n", "        loss.backward()\n", "        optimizer.step()\n", "        total_loss += loss.item()\n", "        num_batches += 1\n", "\n", "    return total_loss / num_batches if num_batches > 0 else 0\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 5, "id": "027e7cd0", "metadata": {}, "outputs": [], "source": ["def evaluate(model, dataloader, device):\n", "    model.eval()\n", "    all_preds, all_labels = [], []\n", "\n", "    with torch.no_grad():\n", "        for points, labels in dataloader:\n", "            # Move data to device\n", "            points = points.to(device)\n", "            labels = labels.to(device)\n", "            \n", "            outputs = model(points)\n", "            preds = outputs.argmax(dim=2)\n", "            all_preds.extend(preds.view(-1).cpu().numpy())\n", "            all_labels.extend(labels.view(-1).cpu().numpy())\n", "\n", "    print(\"\\nEvaluation Report:\\n\")\n", "    print(classification_report(all_labels, all_preds, zero_division=0))\n", "\n", "    # Confusion Matrix\n", "    cm = confusion_matrix(all_labels, all_preds)\n", "    plt.figure(figsize=(8, 6))\n", "    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues')\n", "    plt.xlabel(\"Predicted\")\n", "    plt.ylabel(\"True\")\n", "    plt.title(\"Confusion Matrix\")\n", "    plt.show()\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 6, "id": "fa043f6a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found 368 clusters with both ply and npy files.\n", "Checking label distribution...\n", "Sample label distribution: Counter({4: 1524, 2: 451})\n", "Training files: 294, Validation files: 74\n", "Using device: cpu\n", "Class weights: [10.862988042068865, 11.462450592885375, 1.2184677041418206]\n", "\n", "Starting training...\n", "Epoch  1, Loss: 3292.766067\n", "Epoch  2, Loss: 309.206314\n", "Epoch  3, Loss: 224.804007\n", "Epoch  4, Loss: 95.887431\n", "Epoch  5, Loss: 28.291992\n", "Validation at epoch 5:\n", "\n", "Evaluation Report:\n", "\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.00      0.00      0.00       640\n", "           1       0.00      0.00      0.00       768\n", "           2       0.85      1.00      0.92      8064\n", "\n", "    accuracy                           0.85      9472\n", "   macro avg       0.28      0.33      0.31      9472\n", "weighted avg       0.72      0.85      0.78      9472\n", "\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch  6, Loss: 9.307914\n", "Epoch  7, Loss: 1.538609\n", "Epoch  8, Loss: 1.186984\n", "Epoch  9, Loss: 1.076603\n", "Epoch 10, Loss: 1.030454\n", "Validation at epoch 10:\n", "\n", "Evaluation Report:\n", "\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.00      0.00      0.00       640\n", "           1       0.00      0.00      0.00       768\n", "           2       0.85      1.00      0.92      8064\n", "\n", "    accuracy                           0.85      9472\n", "   macro avg       0.28      0.33      0.31      9472\n", "weighted avg       0.72      0.85      0.78      9472\n", "\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 11, Loss: 1.041275\n", "Epoch 12, Loss: 1.057216\n", "Epoch 13, Loss: 1.025247\n", "Epoch 14, Loss: 1.009330\n", "Epoch 15, Loss: 1.020777\n", "Validation at epoch 15:\n", "\n", "Evaluation Report:\n", "\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.00      0.00      0.00       640\n", "           1       0.00      0.00      0.00       768\n", "           2       0.85      1.00      0.92      8064\n", "\n", "    accuracy                           0.85      9472\n", "   macro avg       0.28      0.33      0.31      9472\n", "weighted avg       0.72      0.85      0.78      9472\n", "\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 16, Loss: 1.029590\n", "Epoch 17, Loss: 1.048681\n", "Epoch 18, Loss: 1.020082\n", "Epoch 19, Loss: 1.035729\n", "Epoch 20, Loss: 1.015561\n", "Validation at epoch 20:\n", "\n", "Evaluation Report:\n", "\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.00      0.00      0.00       640\n", "           1       0.00      0.00      0.00       768\n", "           2       0.85      1.00      0.92      8064\n", "\n", "    accuracy                           0.85      9472\n", "   macro avg       0.28      0.33      0.31      9472\n", "weighted avg       0.72      0.85      0.78      9472\n", "\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Final evaluation:\n", "\n", "Evaluation Report:\n", "\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.00      0.00      0.00       640\n", "           1       0.00      0.00      0.00       768\n", "           2       0.85      1.00      0.92      8064\n", "\n", "    accuracy                           0.85      9472\n", "   macro avg       0.28      0.33      0.31      9472\n", "weighted avg       0.72      0.85      0.78      9472\n", "\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Model saved successfully!\n"]}], "source": ["if __name__ == '__main__':\n", "    # Set random seeds for reproducibility\n", "    torch.manual_seed(42)\n", "    np.random.seed(42)\n", "    \n", "    data_dir = \"labeled_clusters\"\n", "    all_files = os.listdir(data_dir)\n", "\n", "    # Match files with both .ply and .npy\n", "    ply_bases = set(f[:-4] for f in all_files if f.endswith('.ply'))\n", "    npy_bases = set(f[:-4] for f in all_files if f.endswith('.npy'))\n", "    base_names = sorted(list(ply_bases & npy_bases))\n", "\n", "    print(f\"Found {len(base_names)} clusters with both ply and npy files.\")\n", "\n", "    # Check actual label distribution first\n", "    print(\"Checking label distribution...\")\n", "    all_labels = []\n", "    for fname in base_names[:10]:  # Check first 10 files\n", "        labels = np.load(os.path.join(data_dir, fname + '.npy'))\n", "        all_labels.extend(labels.tolist())\n", "    print(\"Sample label distribution:\", Counter(all_labels))\n", "\n", "    # === Hyperparameters ===\n", "    split_ratio = 0.8\n", "    num_points = 128\n", "    batch_size = 4\n", "    num_epochs = 20\n", "    learning_rate = 0.001\n", "    num_classes = 3   # 3 classes (pallet, machinery, noise)\n", "\n", "    # === Label mapping ===\n", "    label_map = {2: 0, 3: 1, 4: 2}  # maps original labels 2,3,4 to indices 0,1,2\n", "\n", "    # === Dataset split ===\n", "    split_idx = int(split_ratio * len(base_names))\n", "    train_files = base_names[:split_idx]\n", "    val_files = base_names[split_idx:]\n", "\n", "    print(f\"Training files: {len(train_files)}, Validation files: {len(val_files)}\")\n", "\n", "    # === Datasets and DataLoaders ===\n", "    train_dataset = PilePointCloudDataset(train_files, data_dir, num_points, num_classes, label_map)\n", "    val_dataset = PilePointCloudDataset(val_files, data_dir, num_points, num_classes, label_map)\n", "\n", "    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)\n", "    val_loader = DataLoader(val_dataset, batch_size=batch_size)\n", "\n", "    # === Initialize Model ===\n", "    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "    print(f\"Using device: {device}\")\n", "    \n", "    model = PointNetPlusPlusPointWise(num_classes)\n", "    model = model.to(device)\n", "\n", "    # === Class Weights ===\n", "    raw_counts = {\n", "        0: 6941,   # class 2 (pallet) → index 0\n", "        1: 6578,   # class 3 (machinery) → index 1  \n", "        2: 61881   # class 4 (noise) → index 2\n", "    }\n", "\n", "    total = sum(raw_counts.values())\n", "    class_weights = [total / raw_counts[i] for i in range(num_classes)]\n", "    print(f\"Class weights: {class_weights}\")\n", "\n", "    class_weights = torch.FloatTensor(class_weights).to(device)\n", "    criterion = nn.CrossEntropyLoss(weight=class_weights)\n", "    optimizer = optim.Adam(model.parameters(), lr=learning_rate)\n", "\n", "    # === Training Loop ===\n", "    print(\"\\nStarting training...\")\n", "    for epoch in range(1, num_epochs + 1):\n", "        loss = train(model, train_loader, optimizer, criterion, device)\n", "        print(f\"Epoch {epoch:2d}, Loss: {loss:.6f}\")\n", "        \n", "        # Validate every 5 epochs\n", "        if epoch % 5 == 0:\n", "            print(f\"Validation at epoch {epoch}:\")\n", "            evaluate(model, val_loader, device)\n", "\n", "    # === Final Evaluation ===\n", "    print(\"\\nFinal evaluation:\")\n", "    evaluate(model, val_loader, device)\n", "\n", "    # === Save Model ===\n", "    torch.save(model.state_dict(), 'pointnetpp_pointwise_model.pt')\n", "    print(\"Model saved successfully!\")"]}, {"cell_type": "code", "execution_count": 7, "id": "f340ffe8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found 368 clusters with both ply and npy files.\n", "Checking label distribution...\n", "Sample label distribution: Counter({4: 1524, 2: 451})\n", "Training files: 294, Validation files: 74\n", "Using device: cpu\n", "Class weights: [29.6, 24.666666666666668, 1.1746031746031746]\n", "\n", "Starting training...\n", "Epoch  1, Loss: 50394.372028\n", "Epoch  2, Loss: 3017.883356\n", "Epoch  3, Loss: 629.662426\n", "Epoch  4, Loss: 13.703799\n", "Epoch  5, Loss: 11.347362\n", "Validation at epoch 5:\n", "\n", "Evaluation Report:\n", "\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.00      0.00      0.00       640\n", "           1       0.08      1.00      0.15       768\n", "           2       0.00      0.00      0.00      8064\n", "\n", "    accuracy                           0.08      9472\n", "   macro avg       0.03      0.33      0.05      9472\n", "weighted avg       0.01      0.08      0.01      9472\n", "\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch  6, Loss: 11.002794\n", "Epoch  7, Loss: 10.863413\n", "Epoch  8, Loss: 10.737960\n", "Epoch  9, Loss: 10.625155\n", "Epoch 10, Loss: 10.530210\n", "Validation at epoch 10:\n", "\n", "Evaluation Report:\n", "\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.07      1.00      0.13       640\n", "           1       0.00      0.00      0.00       768\n", "           2       0.00      0.00      0.00      8064\n", "\n", "    accuracy                           0.07      9472\n", "   macro avg       0.02      0.33      0.04      9472\n", "weighted avg       0.00      0.07      0.01      9472\n", "\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 11, Loss: 10.431404\n", "Epoch 12, Loss: 10.352412\n", "Epoch 13, Loss: 10.276815\n", "Epoch 14, Loss: 10.376178\n", "Epoch 15, Loss: 10.148122\n", "Validation at epoch 15:\n", "\n", "Evaluation Report:\n", "\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.07      1.00      0.13       640\n", "           1       0.00      0.00      0.00       768\n", "           2       0.00      0.00      0.00      8064\n", "\n", "    accuracy                           0.07      9472\n", "   macro avg       0.02      0.33      0.04      9472\n", "weighted avg       0.00      0.07      0.01      9472\n", "\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 16, Loss: 10.253934\n", "Epoch 17, Loss: 10.204367\n", "Epoch 18, Loss: 9.992290\n", "Epoch 19, Loss: 9.951990\n", "Epoch 20, Loss: 9.913480\n", "Validation at epoch 20:\n", "\n", "Evaluation Report:\n", "\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.07      1.00      0.13       640\n", "           1       0.00      0.00      0.00       768\n", "           2       0.00      0.00      0.00      8064\n", "\n", "    accuracy                           0.07      9472\n", "   macro avg       0.02      0.33      0.04      9472\n", "weighted avg       0.00      0.07      0.01      9472\n", "\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Final evaluation:\n", "\n", "Evaluation Report:\n", "\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.07      1.00      0.13       640\n", "           1       0.00      0.00      0.00       768\n", "           2       0.00      0.00      0.00      8064\n", "\n", "    accuracy                           0.07      9472\n", "   macro avg       0.02      0.33      0.04      9472\n", "weighted avg       0.00      0.07      0.01      9472\n", "\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Model saved successfully!\n"]}], "source": ["import os\n", "import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "import torch.optim as optim\n", "from torch.utils.data import Dataset, DataLoader\n", "import numpy as np\n", "import open3d as o3d\n", "from pathlib import Path\n", "import json\n", "import matplotlib.pyplot as plt\n", "from sklearn.metrics import confusion_matrix, classification_report\n", "import seaborn as sns\n", "from collections import Counter\n", "\n", "\n", "class PilePointCloudDataset(Dataset):\n", "    def __init__(self, base_names, data_dir=\"labeled_clusters\", num_points=128, num_classes=3, label_map=None):\n", "        self.data_dir = Path(data_dir)\n", "        self.base_names = base_names\n", "        self.num_points = num_points\n", "        self.num_classes = num_classes\n", "        self.label_map = label_map or {}\n", "\n", "    def __len__(self):\n", "        return len(self.base_names)\n", "\n", "    def __getitem__(self, idx):\n", "        base_name = self.base_names[idx]\n", "        ply_file = self.data_dir / f\"{base_name}.ply\"\n", "        npy_file = self.data_dir / f\"{base_name}.npy\"\n", "\n", "        # Load point cloud\n", "        pcd = o3d.io.read_point_cloud(str(ply_file))\n", "        points = np.asarray(pcd.points)\n", "\n", "        # Load labels\n", "        labels = np.load(npy_file)\n", "        \n", "        # Apply label remapping\n", "        if self.label_map:\n", "            remapped_labels = np.zeros_like(labels)\n", "            for old_label, new_label in self.label_map.items():\n", "                remapped_labels[labels == old_label] = new_label\n", "            labels = remapped_labels\n", "        \n", "        # Sanity check\n", "        if len(points) != len(labels):\n", "            raise ValueError(f\"Mismatch: {len(points)} points vs {len(labels)} labels in {base_name}\")\n", "\n", "        # Sample or pad points with different random state each time\n", "        np.random.seed(None)  # Ensure different sampling each epoch\n", "        if len(points) < self.num_points:\n", "            indices = np.random.choice(len(points), self.num_points, replace=True)\n", "        else:\n", "            indices = np.random.choice(len(points), self.num_points, replace=False)\n", "\n", "        sampled_points = points[indices]\n", "        sampled_labels = labels[indices]\n", "\n", "        # Clip label values to avoid out-of-bound errors\n", "        sampled_labels = np.clip(sampled_labels, 0, self.num_classes - 1)\n", "\n", "        return torch.tensor(sampled_points, dtype=torch.float32), torch.tensor(sampled_labels, dtype=torch.long)\n", "\n", "\n", "class FocalLoss(nn.Module):\n", "    def __init__(self, alpha=None, gamma=2.0, weight=None):\n", "        super().__init__()\n", "        self.alpha = alpha\n", "        self.gamma = gamma\n", "        self.weight = weight\n", "        \n", "    def forward(self, inputs, targets):\n", "        ce_loss = F.cross_entropy(inputs, targets, weight=self.weight, reduction='none')\n", "        pt = torch.exp(-ce_loss)\n", "        focal_loss = (1 - pt) ** self.gamma * ce_loss\n", "        \n", "        if self.alpha is not None:\n", "            alpha_t = self.alpha[targets]\n", "            focal_loss = alpha_t * focal_loss\n", "            \n", "        return focal_loss.mean()\n", "\n", "\n", "class PointNetPlusPlusPointWise(nn.Module):\n", "    def __init__(self, num_classes):\n", "        super().__init__()\n", "        \n", "        self.mlp1 = nn.Sequential(\n", "            nn.<PERSON><PERSON>(3, 64), nn.<PERSON><PERSON>(),\n", "            nn.<PERSON><PERSON>(64, 64), nn.<PERSON><PERSON><PERSON>()\n", "        )\n", "        self.mlp2 = nn.Sequential(\n", "            nn.<PERSON><PERSON>(64, 128), nn.<PERSON><PERSON>(),\n", "            nn.<PERSON><PERSON>(128, 256), nn.<PERSON><PERSON><PERSON>()\n", "        )\n", "        \n", "        self.classifier = nn.Sequential(\n", "            nn.<PERSON><PERSON>(256, 256), nn.<PERSON><PERSON><PERSON>(),\n", "            nn.Dropout(0.3),  # Add dropout to prevent overfitting\n", "            nn.Linear(256, num_classes)\n", "        )\n", "\n", "    def forward(self, x):\n", "        # Input: [B, N, 3]\n", "        x = self.mlp1(x)\n", "        x = self.mlp2(x)\n", "        out = self.classifier(x)  # [B, N, num_classes]\n", "        return out\n", "\n", "\n", "def train(model, dataloader, optimizer, criterion, device):\n", "    model.train()\n", "    total_loss = 0\n", "    num_batches = 0\n", "\n", "    for points, labels in dataloader:\n", "        # Move data to device\n", "        points = points.to(device)\n", "        labels = labels.to(device)\n", "        \n", "        optimizer.zero_grad()\n", "        outputs = model(points)\n", "        outputs = outputs.view(-1, outputs.shape[-1])  # [B*N, C]\n", "        labels = labels.view(-1)  # [B*N]\n", "        loss = criterion(outputs, labels)\n", "        loss.backward()\n", "        optimizer.step()\n", "        total_loss += loss.item()\n", "        num_batches += 1\n", "\n", "    return total_loss / num_batches if num_batches > 0 else 0\n", "\n", "\n", "def evaluate(model, dataloader, device):\n", "    model.eval()\n", "    all_preds, all_labels = [], []\n", "\n", "    with torch.no_grad():\n", "        for points, labels in dataloader:\n", "            # Move data to device\n", "            points = points.to(device)\n", "            labels = labels.to(device)\n", "            \n", "            outputs = model(points)\n", "            preds = outputs.argmax(dim=2)\n", "            all_preds.extend(preds.view(-1).cpu().numpy())\n", "            all_labels.extend(labels.view(-1).cpu().numpy())\n", "\n", "    print(\"\\nEvaluation Report:\\n\")\n", "    print(classification_report(all_labels, all_preds, zero_division=0))\n", "\n", "    # Confusion Matrix\n", "    cm = confusion_matrix(all_labels, all_preds)\n", "    plt.figure(figsize=(8, 6))\n", "    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues')\n", "    plt.xlabel(\"Predicted\")\n", "    plt.ylabel(\"True\")\n", "    plt.title(\"Confusion Matrix\")\n", "    plt.show()\n", "\n", "\n", "if __name__ == '__main__':\n", "    # Set random seeds for reproducibility\n", "    torch.manual_seed(42)\n", "    np.random.seed(42)\n", "    \n", "    data_dir = \"labeled_clusters\"\n", "    all_files = os.listdir(data_dir)\n", "\n", "    # Match files with both .ply and .npy\n", "    ply_bases = set(f[:-4] for f in all_files if f.endswith('.ply'))\n", "    npy_bases = set(f[:-4] for f in all_files if f.endswith('.npy'))\n", "    base_names = sorted(list(ply_bases & npy_bases))\n", "\n", "    print(f\"Found {len(base_names)} clusters with both ply and npy files.\")\n", "\n", "    # Check actual label distribution first\n", "    print(\"Checking label distribution...\")\n", "    all_labels = []\n", "    for fname in base_names[:10]:  # Check first 10 files\n", "        labels = np.load(os.path.join(data_dir, fname + '.npy'))\n", "        all_labels.extend(labels.tolist())\n", "    print(\"Sample label distribution:\", Counter(all_labels))\n", "\n", "    # === Hyperparameters ===\n", "    split_ratio = 0.8\n", "    num_points = 128\n", "    batch_size = 4\n", "    num_epochs = 20\n", "    learning_rate = 0.001\n", "    num_classes = 3   # 3 classes (pallet, machinery, noise)\n", "\n", "    # === Label mapping ===\n", "    label_map = {2: 0, 3: 1, 4: 2}  # maps original labels 2,3,4 to indices 0,1,2\n", "\n", "    # === Dataset split ===\n", "    split_idx = int(split_ratio * len(base_names))\n", "    train_files = base_names[:split_idx]\n", "    val_files = base_names[split_idx:]\n", "\n", "    print(f\"Training files: {len(train_files)}, Validation files: {len(val_files)}\")\n", "\n", "    # === Datasets and DataLoaders ===\n", "    train_dataset = PilePointCloudDataset(train_files, data_dir, num_points, num_classes, label_map)\n", "    val_dataset = PilePointCloudDataset(val_files, data_dir, num_points, num_classes, label_map)\n", "\n", "    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)\n", "    val_loader = DataLoader(val_dataset, batch_size=batch_size)\n", "\n", "    # === Initialize Model ===\n", "    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "    print(f\"Using device: {device}\")\n", "    \n", "    model = PointNetPlusPlusPointWise(num_classes)\n", "    model = model.to(device)\n", "\n", "    # === Class Weights - More aggressive weighting ===\n", "    raw_counts = {\n", "        0: 640,    # actual validation counts from your run\n", "        1: 768,    # actual validation counts from your run  \n", "        2: 8064    # actual validation counts from your run\n", "    }\n", "\n", "    # Try more aggressive inverse frequency weighting\n", "    total = sum(raw_counts.values())\n", "    class_weights = [total / raw_counts[i] for i in range(num_classes)]\n", "    \n", "    # Apply additional scaling to minority classes\n", "    class_weights[0] *= 2.0  # Extra boost for class 0\n", "    class_weights[1] *= 2.0  # Extra boost for class 1\n", "    \n", "    print(f\"Class weights: {class_weights}\")\n", "\n", "    class_weights = torch.FloatTensor(class_weights).to(device)\n", "    \n", "    # Try focal loss for better handling of class imbalance\n", "    alpha = torch.tensor([2.0, 2.0, 1.0]).to(device)  # Give more focus to minority classes\n", "    criterion = FocalLoss(alpha=alpha, gamma=2.0, weight=class_weights)\n", "    # Alternative: criterion = nn.CrossEntropyLoss(weight=class_weights)\n", "    \n", "    optimizer = optim.<PERSON>(model.parameters(), lr=learning_rate, weight_decay=1e-4)\n", "\n", "    # === Training Loop ===\n", "    print(\"\\nStarting training...\")\n", "    for epoch in range(1, num_epochs + 1):\n", "        loss = train(model, train_loader, optimizer, criterion, device)\n", "        print(f\"Epoch {epoch:2d}, Loss: {loss:.6f}\")\n", "        \n", "        # Validate every 5 epochs\n", "        if epoch % 5 == 0:\n", "            print(f\"Validation at epoch {epoch}:\")\n", "            evaluate(model, val_loader, device)\n", "\n", "    # === Final Evaluation ===\n", "    print(\"\\nFinal evaluation:\")\n", "    evaluate(model, val_loader, device)\n", "\n", "    # === Save Model ===\n", "    torch.save(model.state_dict(), 'pointnetpp_pointwise_model.pt')\n", "    print(\"Model saved successfully!\")"]}, {"cell_type": "code", "execution_count": null, "id": "7523c7d5", "metadata": {}, "outputs": [], "source": ["from collections import Counter\n", "all_labels = []\n", "for fname in base_names:\n", "    labels = np.load(os.path.join(data_dir, fname + '.npy'))\n", "    all_labels.extend(labels.tolist())\n", "print(Counter(all_labels))\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}