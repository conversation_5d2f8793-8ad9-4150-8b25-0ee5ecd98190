{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# PointNet++ Semantic Segmentation for Pile Clusters\n", "\n", "This notebook implements **PointNet++** for semantic segmentation within DBSCAN clusters to classify points into:\n", "- **pile**: Main foundation pile structure\n", "- **support**: Support structures/scaffolding\n", "- **pallet**: Storage pallets\n", "- **non-pile**: Ground, debris, other objects\n", "\n", "**PointNet++** provides hierarchical feature learning and better geometric understanding compared to PointNet.\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025  \n", "**Project**: As-Built Foundation Analysis - Semantic Segmentation Stage\n", "\n", "## Learning Objectives\n", "1. Understand PointNet++ architecture components\n", "2. Learn hierarchical point cloud processing\n", "3. Implement semantic segmentation for construction elements\n", "4. Analyze pile detection results\n", "\n", "## Compatibility Notes\n", "- This notebook is compatible with NumPy >= 1.20.0 (uses `np.int64` instead of deprecated `np.long`)\n", "- Requires PyTorch >= 1.8.0 for optimal performance\n", "- Open3D >= 0.13.0 recommended for point cloud processing"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Import Libraries and Setup\n", "\n", "Let's start by importing the necessary libraries for deep learning, point cloud processing, and visualization."]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["NumPy version: 1.26.4\n", "PyTorch version: 2.5.1\n", "Using device: cpu\n"]}], "source": ["# Core libraries\n", "import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "import torch.optim as optim\n", "from torch.utils.data import Dataset, DataLoader\n", "\n", "# Data processing and visualization\n", "import numpy as np\n", "import open3d as o3d\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "# File and path utilities\n", "from pathlib import Path\n", "import json\n", "import pickle\n", "from datetime import datetime\n", "import warnings\n", "\n", "# Suppress warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Print versions for compatibility checks\n", "print(f\"NumPy version: {np.__version__}\")\n", "print(f\"PyTorch version: {torch.__version__}\")\n", "\n", "# Select device\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f\"Using device: {device}\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. PointNet++ Core Functions\n", "\n", "PointNet++ uses several key operations for hierarchical point processing. Let's implement these step by step."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.1 Distance and Indexing Functions\n", "\n", "These functions help us calculate distances between points and index them efficiently."]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["def square_distance(src, dst):\n", "    \"\"\"\n", "    Calculate Euclidean distance between each two points.\n", "    Input:\n", "        src: source points, [B, N, C]\n", "        dst: target points, [B, M, C]\n", "    Output:\n", "        dist: per-point square distance, [B, N, M]\n", "    \"\"\"\n", "    B, N, _ = src.shape\n", "    _, M, _ = dst.shape\n", "    dist = -2 * torch.matmul(src, dst.permute(0, 2, 1))     # -2xy^T\n", "    dist += torch.sum(src ** 2, -1).view(B, N, 1)            # +x^2\n", "    dist += torch.sum(dst ** 2, -1).view(B, 1, M)            # +y^2\n", "    return dist\n", "\n", "def index_points(points, idx):\n", "    \"\"\"\n", "    Index points along the second dimension.\n", "    Input:\n", "        points: input points data, [B, N, C]\n", "        idx: sample index data, [B, S] or [B, S, K]\n", "    Return:\n", "        new_points: indexed points data, [B, S, C] or [B, S, K, C]\n", "    \"\"\"\n", "    device = points.device\n", "    B = points.shape[0]\n", "    view_shape = list(idx.shape)\n", "    view_shape[1:] = [1] * (len(view_shape) - 1)   # e.g., [B, 1, 1]\n", "    repeat_shape = list(idx.shape)\n", "    repeat_shape[0] = 1\n", "    batch_indices = torch.arange(B, dtype=torch.long).to(device).view(view_shape).repeat(repeat_shape)\n", "    new_points = points[batch_indices, idx, :]\n", "    return new_points\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.2 Farthest Point Sampling\n", "\n", "This function selects representative points that are spread out across the point cloud."]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["def farthest_point_sample(xyz, npoint):\n", "    \"\"\"Farthest Point Sampling\"\"\"\n", "    device = xyz.device\n", "    B, N, C = xyz.shape\n", "    centroids = torch.zeros(B, npoint, dtype=torch.long).to(device)\n", "    distance = torch.ones(B, N).to(device) * 1e10\n", "    farthest = torch.randint(0, N, (B,), dtype=torch.long).to(device)\n", "    batch_indices = torch.arange(B, dtype=torch.long).to(device)\n", "    \n", "    for i in range(npoint):\n", "        centroids[:, i] = farthest\n", "        centroid = xyz[batch_indices, farthest, :].view(B, 1, 3)\n", "        dist = torch.sum((xyz - centroid) ** 2, -1)\n", "        mask = dist < distance\n", "        distance[mask] = dist[mask]\n", "        farthest = torch.max(distance, -1)[1]\n", "    \n", "    return centroids"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.3 Ball Query\n", "\n", "This function finds neighboring points within a specified radius."]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["def query_ball_point(radius, nsample, xyz, new_xyz):\n", "    \"\"\"Ball query\"\"\"\n", "    device = xyz.device\n", "    B, N, C = xyz.shape\n", "    _, S, _ = new_xyz.shape\n", "    group_idx = torch.arange(N, dtype=torch.long).to(device).view(1, 1, N).repeat([B, S, 1])\n", "    sqrdists = square_distance(new_xyz, xyz)\n", "    group_idx[sqrdists > radius ** 2] = N\n", "    group_idx = group_idx.sort(dim=-1)[0][:, :, :nsample]\n", "    group_first = group_idx[:, :, 0].view(B, S, 1).repeat([1, 1, nsample])\n", "    mask = group_idx == N\n", "    group_idx[mask] = group_first[mask]\n", "    return group_idx"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.4 Sampling and Grouping Functions\n", "\n", "These functions combine sampling and grouping operations for hierarchical processing."]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["def sample_and_group(npoint, radius, nsample, xyz, points, returnfps=False):\n", "    \"\"\"Sample and group points for hierarchical processing\"\"\"\n", "    B, N, C = xyz.shape\n", "    S = npoint\n", "    \n", "    # Sample points using farthest point sampling\n", "    fps_idx = farthest_point_sample(xyz, npoint)  # [B, npoint]\n", "    new_xyz = index_points(xyz, fps_idx)\n", "    \n", "    # Group neighboring points\n", "    idx = query_ball_point(radius, nsample, xyz, new_xyz)\n", "    grouped_xyz = index_points(xyz, idx)  # [B, npoint, nsample, C]\n", "    grouped_xyz_norm = grouped_xyz - new_xyz.view(B, S, 1, C)\n", "    \n", "    if points is not None:\n", "        grouped_points = index_points(points, idx)\n", "        new_points = torch.cat([grouped_xyz_norm, grouped_points], dim=-1)  # [B, npoint, nsample, C+D]\n", "    else:\n", "        new_points = grouped_xyz_norm\n", "    \n", "    if returnfps:\n", "        return new_xyz, new_points, grouped_xyz, fps_idx\n", "    else:\n", "        return new_xyz, new_points\n", "\n", "def sample_and_group_all(xyz, points):\n", "    \"\"\"Group all points (used in final layer)\"\"\"\n", "    device = xyz.device\n", "    B, N, C = xyz.shape\n", "    new_xyz = torch.zeros(B, 1, C).to(device)\n", "    grouped_xyz = xyz.view(B, 1, N, C)\n", "    if points is not None:\n", "        new_points = torch.cat([grouped_xyz, points.view(B, 1, N, -1)], dim=-1)\n", "    else:\n", "        new_points = grouped_xyz\n", "    return new_xyz, new_points"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. PointNet++ Architecture Components\n", "\n", "Now let's build the main components of the PointNet++ architecture."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.1 Set Abstraction Layer\n", "\n", "This layer reduces the number of points while extracting features."]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["class PointNetSetAbstraction(nn.Module):\n", "    \"\"\"PointNet Set Abstraction Layer\"\"\"\n", "    def __init__(self, npoint, radius, nsample, in_channel, mlp, group_all):\n", "        super(PointNetSetAbstraction, self).__init__()\n", "        self.npoint = npoint\n", "        self.radius = radius\n", "        self.nsample = nsample\n", "        self.mlp_convs = nn.ModuleList()\n", "        self.mlp_bns = nn.ModuleList()\n", "        self.group_all = group_all\n", "\n", "        last_channel = in_channel\n", "\n", "        # Build MLP layers\n", "        for out_channel in mlp:\n", "            self.mlp_convs.append(nn.Conv2d(last_channel, out_channel, 1))\n", "            self.mlp_bns.append(nn.BatchNorm2d(out_channel))\n", "            last_channel = out_channel\n", "\n", "    def forward(self, xyz, points):\n", "        xyz = xyz.permute(0, 2, 1)\n", "        if points is not None:\n", "            points = points.permute(0, 2, 1)\n", "\n", "        # if self.group_all:\n", "        #     new_xyz, new_points = sample_and_group_all(xyz, points)\n", "        # else:\n", "        #     new_xyz, new_points = sample_and_group(self.npoint, self.radius, self.nsample, xyz, points)\n", "\n", "        if self.group_all:\n", "            new_xyz = torch.zeros(xyz.shape[0], 1, xyz.shape[2]).to(xyz.device)\n", "            new_points = xyz.view(xyz.shape[0], 1, xyz.shape[1], xyz.shape[2])\n", "            if points is not None:\n", "                new_points = torch.cat([new_points, points.view(points.shape[0], 1, points.shape[1], points.shape[2])], dim=-1)\n", "        else:\n", "            fps_idx = farthest_point_sample(xyz, self.npoint)\n", "            new_xyz = index_points(xyz, fps_idx)\n", "            idx = query_ball_point(self.radius, self.nsample, xyz, new_xyz)\n", "            grouped_xyz = index_points(xyz, idx)\n", "            grouped_xyz_norm = grouped_xyz - new_xyz.view(xyz.shape[0], self.npoint, 1, xyz.shape[2])\n", "            \n", "            if points is not None:\n", "                grouped_points = index_points(points, idx)\n", "                new_points = torch.cat([grouped_xyz_norm, grouped_points], dim=-1)\n", "            else:\n", "                new_points = grouped_xyz_norm\n", "\n", "        # Apply MLP to grouped points\n", "        new_points = new_points.permute(0, 3, 2, 1)  # [B, C+D, nsample, npoint]\n", "        for i, conv in enumerate(self.mlp_convs):\n", "            bn = self.mlp_bns[i]\n", "            new_points = <PERSON>.relu(bn(conv(new_points)))\n", "\n", "        # Max pooling to get point-wise features\n", "        new_points = torch.max(new_points, 2)[0]\n", "        new_xyz = new_xyz.permute(0, 2, 1)\n", "        return new_xyz, new_points"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.2 Feature Propagation Layer\n", "\n", "This layer upsamples features back to the original resolution."]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["class PointNetFeaturePropagation(nn.Module):\n", "    \"\"\"Feature Propagation Layer for PointNet++\"\"\"\n", "    def __init__(self, in_channel, mlp):\n", "        super(PointNetFeaturePropagation, self).__init__()\n", "        self.mlp_convs = nn.ModuleList()\n", "        self.mlp_bns = nn.ModuleList()\n", "        last_channel = in_channel\n", "        \n", "        for out_channel in mlp:\n", "            self.mlp_convs.append(nn.Conv1d(last_channel, out_channel, 1))\n", "            self.mlp_bns.append(nn.BatchNorm1d(out_channel))\n", "            last_channel = out_channel\n", "\n", "    def forward(self, xyz1, xyz2, points1, points2):\n", "        xyz1 = xyz1.permute(0, 2, 1)\n", "        xyz2 = xyz2.permute(0, 2, 1)\n", "        points2 = points2.permute(0, 2, 1)\n", "        B, N, C = xyz1.shape\n", "        _, S, _ = xyz2.shape\n", "\n", "        if S == 1:\n", "            interpolated_points = points2.repeat(1, N, 1)\n", "        else:\n", "            # Interpolate using inverse distance weighting\n", "            dists = square_distance(xyz1, xyz2)\n", "            dists, idx = dists.sort(dim=-1)\n", "            dists, idx = dists[:, :, :3], idx[:, :, :3]  # [B, N, 3]\n", "\n", "            dist_recip = 1.0 / (dists + 1e-8)\n", "            norm = torch.sum(dist_recip, dim=2, keepdim=True)\n", "            weight = dist_recip / norm\n", "            interpolated_points = torch.sum(index_points(points2, idx) * weight.view(B, N, 3, 1), dim=2)\n", "\n", "        if points1 is not None:\n", "            points1 = points1.permute(0, 2, 1)\n", "            new_points = torch.cat([points1, interpolated_points], dim=-1)\n", "        else:\n", "            new_points = interpolated_points\n", "\n", "        new_points = new_points.permute(0, 2, 1)\n", "        for i, conv in enumerate(self.mlp_convs):\n", "            bn = self.mlp_bns[i]\n", "            new_points = <PERSON>.relu(bn(conv(new_points)))\n", "        return new_points"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["# Not needed - as we used DBSCAN for clustering\n", "class PileBufferSegmentation(nn.Module):\n", "    \"\"\"\n", "    PointNet++ optimized for pile buffer zone segmentation\n", "    Designed for distinguishing piles from clutter in buffered regions\n", "    \"\"\"\n", "    def __init__(self, num_classes=6):  # pile, vegetation, jcb, mud, pallet, other\n", "        super(PileBufferSegmentation, self).__init__()\n", "        self.num_classes = num_classes\n", "        \n", "        # Enhanced feature extraction for construction objects\n", "        # Smaller radii for detailed local features important for construction objects\n", "        self.sa1 = PointNetSetAbstraction(1024, 0.1, 32, 3, [32, 32, 64], False)\n", "        self.sa2 = PointNetSetAbstraction(256, 0.2, 32, 64 + 3, [64, 64, 128], False)\n", "        self.sa3 = PointNetSetAbstraction(64, 0.4, 32, 128 + 3, [128, 128, 256], False)\n", "        self.sa4 = PointNetSetAbstraction(16, 0.8, 32, 256 + 3, [256, 256, 512], False)\n", "        self.sa5 = PointNetSetAbstraction(None, None, None, 512 + 3, [512, 512, 1024], True)\n", "        \n", "        # Feature propagation with enhanced channels for detailed reconstruction\n", "        self.fp5 = PointNetFeaturePropagation(1536, [512, 512])\n", "        self.fp4 = PointNetFeaturePropagation(768, [512, 256])\n", "        self.fp3 = PointNetFeaturePropagation(384, [256, 256])\n", "        self.fp2 = PointNetFeaturePropagation(320, [256, 128])\n", "        self.fp1 = PointNetFeaturePropagation(128, [128, 128, 128])\n", "        \n", "        # Enhanced classification head with attention mechanism\n", "        self.conv1 = nn.Conv1d(128, 128, 1)\n", "        self.bn1 = nn.BatchNorm1d(128)\n", "        self.drop1 = nn.Dropout(0.3)\n", "        \n", "        # Attention mechanism for important feature selection\n", "        self.attention = nn.Conv1d(128, 128, 1)\n", "        self.attention_bn = nn.BatchNorm1d(128)\n", "        \n", "        self.conv2 = nn.Conv1d(128, 64, 1)\n", "        self.bn2 = nn.BatchNorm1d(64)\n", "        self.drop2 = nn.Dropout(0.3)\n", "        \n", "        self.conv3 = nn.Conv1d(64, num_classes, 1)\n", "\n", "    def forward(self, xyz):\n", "        B, _, N = xyz.shape\n", "        \n", "        # Hierarchical feature extraction with more levels for detailed analysis\n", "        l1_xyz, l1_points = self.sa1(xyz, None)      # 2048 -> 1024\n", "        l2_xyz, l2_points = self.sa2(l1_xyz, l1_points)  # 1024 -> 256  \n", "        l3_xyz, l3_points = self.sa3(l2_xyz, l2_points)  # 256 -> 64\n", "        l4_xyz, l4_points = self.sa4(l3_xyz, l3_points)  # 64 -> 16\n", "        l5_xyz, l5_points = self.sa5(l4_xyz, l4_points)  # 16 -> 1\n", "        \n", "        # Hierarchical feature propagation\n", "        l4_points = self.fp5(l4_xyz, l5_xyz, l4_points, l5_points)\n", "        l3_points = self.fp4(l3_xyz, l4_xyz, l3_points, l4_points)\n", "        l2_points = self.fp3(l2_xyz, l3_xyz, l2_points, l3_points)\n", "        l1_points = self.fp2(l1_xyz, l2_xyz, l1_points, l2_points)\n", "        l0_points = self.fp1(xyz, l1_xyz, None, l1_points)\n", "        \n", "        # Enhanced classification with attention\n", "        feat = self.drop1(<PERSON><PERSON>relu(self.bn1(self.conv1(l0_points))))\n", "        \n", "        # Apply attention mechanism\n", "        attention_weights = torch.sigmoid(self.attention_bn(self.attention(feat)))\n", "        feat = feat * attention_weights\n", "        \n", "        feat = self.drop2(<PERSON><PERSON>relu(self.bn2(self.conv2(feat))))\n", "        feat = self.conv3(feat)\n", "        \n", "        return feat.permute(0, 2, 1)  # [B, N, num_classes]\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.4 Sampling and Grouping Functions\n", "\n", "These functions combine sampling and grouping operations for hierarchical processing."]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["def sample_and_group(npoint, radius, nsample, xyz, points, returnfps=False):\n", "    \"\"\"Sample and group points for hierarchical processing\"\"\"\n", "    B, N, C = xyz.shape\n", "    S = npoint\n", "    \n", "    # Sample points using farthest point sampling\n", "    fps_idx = farthest_point_sample(xyz, npoint)  # [B, npoint]\n", "    new_xyz = index_points(xyz, fps_idx)\n", "    \n", "    # Group neighboring points\n", "    idx = query_ball_point(radius, nsample, xyz, new_xyz)\n", "    grouped_xyz = index_points(xyz, idx)  # [B, npoint, nsample, C]\n", "    grouped_xyz_norm = grouped_xyz - new_xyz.view(B, S, 1, C)\n", "    \n", "    if points is not None:\n", "        grouped_points = index_points(points, idx)\n", "        new_points = torch.cat([grouped_xyz_norm, grouped_points], dim=-1)  # [B, npoint, nsample, C+D]\n", "    else:\n", "        new_points = grouped_xyz_norm\n", "    \n", "    if returnfps:\n", "        return new_xyz, new_points, grouped_xyz, fps_idx\n", "    else:\n", "        return new_xyz, new_points\n", "\n", "def sample_and_group_all(xyz, points):\n", "    \"\"\"Group all points (used in final layer)\"\"\"\n", "    device = xyz.device\n", "    B, N, C = xyz.shape\n", "    new_xyz = torch.zeros(B, 1, C).to(device)\n", "    grouped_xyz = xyz.view(B, 1, N, C)\n", "    if points is not None:\n", "        new_points = torch.cat([grouped_xyz, points.view(B, 1, N, -1)], dim=-1)\n", "    else:\n", "        new_points = grouped_xyz\n", "    return new_xyz, new_points"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. PointNet++ Architecture Components\n", "\n", "Now let's build the main components of the PointNet++ architecture."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.1 Set Abstraction Layer\n", "\n", "This layer reduces the number of points while extracting features."]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["class PointNetSetAbstraction(nn.Module):\n", "    \"\"\"PointNet Set Abstraction Layer\"\"\"\n", "    def __init__(self, npoint, radius, nsample, in_channel, mlp, group_all):\n", "        super(PointNetSetAbstraction, self).__init__()\n", "        self.npoint = npoint\n", "        self.radius = radius\n", "        self.nsample = nsample\n", "        self.mlp_convs = nn.ModuleList()\n", "        self.mlp_bns = nn.ModuleList()\n", "        last_channel = in_channel\n", "        \n", "        # Build MLP layers\n", "        for out_channel in mlp:\n", "            self.mlp_convs.append(nn.Conv2d(last_channel, out_channel, 1))\n", "            self.mlp_bns.append(nn.BatchNorm2d(out_channel))\n", "            last_channel = out_channel\n", "        self.group_all = group_all\n", "\n", "    def forward(self, xyz, points):\n", "        \"\"\"\n", "        Input:\n", "            xyz: input points position data, [B, C, N]\n", "            points: input points data, [B, D, N]\n", "        Return:\n", "            new_xyz: sampled points position data, [B, C, S]\n", "            new_points_concat: sample points feature data, [B, D', S]\n", "        \"\"\"\n", "        xyz = xyz.permute(0, 2, 1)\n", "        if points is not None:\n", "            points = points.permute(0, 2, 1)\n", "\n", "        if self.group_all:\n", "            new_xyz, new_points = sample_and_group_all(xyz, points)\n", "        else:\n", "            new_xyz, new_points = sample_and_group(self.npoint, self.radius, self.nsample, xyz, points)\n", "        \n", "        # Apply MLP to grouped points\n", "        new_points = new_points.permute(0, 3, 2, 1)  # [B, C+D, nsample, npoint]\n", "        for i, conv in enumerate(self.mlp_convs):\n", "            bn = self.mlp_bns[i]\n", "            new_points = <PERSON>.relu(bn(conv(new_points)))\n", "\n", "        # Max pooling to get point-wise features\n", "        new_points = torch.max(new_points, 2)[0]\n", "        new_xyz = new_xyz.permute(0, 2, 1)\n", "        return new_xyz, new_points"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.2 Feature Propagation Layer\n", "\n", "This layer upsamples features back to the original resolution."]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["class PointNetFeaturePropagation(nn.Module):\n", "    \"\"\"Feature Propagation Layer for PointNet++\"\"\"\n", "    def __init__(self, in_channel, mlp):\n", "        super(PointNetFeaturePropagation, self).__init__()\n", "        self.mlp_convs = nn.ModuleList()\n", "        self.mlp_bns = nn.ModuleList()\n", "        last_channel = in_channel\n", "        \n", "        for out_channel in mlp:\n", "            self.mlp_convs.append(nn.Conv1d(last_channel, out_channel, 1))\n", "            self.mlp_bns.append(nn.BatchNorm1d(out_channel))\n", "            last_channel = out_channel\n", "\n", "    def forward(self, xyz1, xyz2, points1, points2):\n", "        \"\"\"\n", "        Input:\n", "            xyz1: input points position data, [B, C, N]\n", "            xyz2: sampled input points position data, [B, C, S]\n", "            points1: input points data, [B, D, N]\n", "            points2: input points data, [B, D, S]\n", "        Return:\n", "            new_points: upsampled points data, [B, D', N]\n", "        \"\"\"\n", "        xyz1 = xyz1.permute(0, 2, 1)\n", "        xyz2 = xyz2.permute(0, 2, 1)\n", "        points2 = points2.permute(0, 2, 1)\n", "        B, N, C = xyz1.shape\n", "        _, S, _ = xyz2.shape\n", "\n", "        if S == 1:\n", "            interpolated_points = points2.repeat(1, N, 1)\n", "        else:\n", "            # Interpolate using inverse distance weighting\n", "            dists = square_distance(xyz1, xyz2)\n", "            dists, idx = dists.sort(dim=-1)\n", "            dists, idx = dists[:, :, :3], idx[:, :, :3]  # [B, N, 3]\n", "\n", "            dist_recip = 1.0 / (dists + 1e-8)\n", "            norm = torch.sum(dist_recip, dim=2, keepdim=True)\n", "            weight = dist_recip / norm\n", "            interpolated_points = torch.sum(index_points(points2, idx) * weight.view(B, N, 3, 1), dim=2)\n", "\n", "        if points1 is not None:\n", "            points1 = points1.permute(0, 2, 1)\n", "            new_points = torch.cat([points1, interpolated_points], dim=-1)\n", "        else:\n", "            new_points = interpolated_points\n", "\n", "        new_points = new_points.permute(0, 2, 1)\n", "        for i, conv in enumerate(self.mlp_convs):\n", "            bn = self.mlp_bns[i]\n", "            new_points = <PERSON>.relu(bn(conv(new_points)))\n", "        return new_points"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.3 Complete PointNet++ Model\n", "\n", "Now let's combine all components into the complete PointNet++ model for semantic segmentation."]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["class PointNetPlusPlusSegmentation(nn.Module):\n", "    \"\"\"PointNet++ for Semantic Segmentation\"\"\"\n", "    def __init__(self, num_classes=4):\n", "        super(PointNetPlusPlusSegmentation, self).__init__()\n", "        self.num_classes = num_classes\n", "        \n", "        # Set Abstraction layers (encoder)\n", "        self.sa1 = PointNetSetAbstraction(npoint=512, radius=0.2, nsample=32, in_channel=3, mlp=[64, 64, 128], group_all=False)\n", "        self.sa2 = PointNetSetAbstraction(npoint=128, radius=0.4, nsample=64, in_channel=128 + 3, mlp=[128, 128, 256], group_all=False)\n", "        self.sa3 = PointNetSetAbstraction(npoint=None, radius=None, nsample=None, in_channel=256 + 3, mlp=[256, 512, 1024], group_all=True)\n", "        \n", "        # Feature Propagation layers (decoder)\n", "        self.fp3 = PointNetFeaturePropagation(in_channel=1280, mlp=[256, 256])\n", "        self.fp2 = PointNetFeaturePropagation(in_channel=384, mlp=[256, 128])\n", "        self.fp1 = PointNetFeaturePropagation(in_channel=128, mlp=[128, 128, 128])\n", "        \n", "        # Classification head\n", "        self.conv1 = nn.Conv1d(128, 128, 1)\n", "        self.bn1 = nn.BatchNorm1d(128)\n", "        self.drop1 = nn.Dropout(0.5)\n", "        self.conv2 = nn.Conv1d(128, num_classes, 1)\n", "\n", "    def forward(self, xyz):\n", "        B, _, N = xyz.shape\n", "        \n", "        # Set Abstraction layers (downsampling)\n", "        l1_xyz, l1_points = self.sa1(xyz, None)\n", "        l2_xyz, l2_points = self.sa2(l1_xyz, l1_points)\n", "        l3_xyz, l3_points = self.sa3(l2_xyz, l2_points)\n", "        \n", "        # Feature Propagation layers (upsampling)\n", "        l2_points = self.fp3(l2_xyz, l3_xyz, l2_points, l3_points)\n", "        l1_points = self.fp2(l1_xyz, l2_xyz, l1_points, l2_points)\n", "        l0_points = self.fp1(xyz, l1_xyz, None, l1_points)\n", "        \n", "        # Classification\n", "        feat = <PERSON><PERSON>relu(self.bn1(self.conv1(l0_points)))\n", "        feat = self.drop1(feat)\n", "        feat = self.conv2(feat)\n", "        feat = F.log_softmax(feat, dim=1)\n", "        \n", "        return feat.permute(0, 2, 1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Dataset Class for Pile Clusters\n", "\n", "Let's create a dataset class to handle our pile cluster data with proper preprocessing."]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [], "source": ["class PileClusterDataset(Dataset):\n", "    \"\"\"Enhanced dataset for PointNet++ with better preprocessing\"\"\"\n", "    \n", "    def __init__(self, cluster_dir, labels_dir=None, num_points=4096, augment=False):\n", "        self.cluster_dir = Path(cluster_dir)\n", "        self.labels_dir = Path(labels_dir) if labels_dir else None\n", "        self.num_points = num_points\n", "        self.augment = augment\n", "        \n", "        # Class mapping for construction elements\n", "        self.class_map = {\n", "            'pile': 0,\n", "            'support': 1, \n", "            'pallet': 2,\n", "            'machinery': 3,\n", "            'non-pile': 4\n", "        }\n", "\n", "        self.num_classes = len(self.class_map)\n", "        \n", "        # Load cluster files\n", "        self.cluster_files = list(self.cluster_dir.glob(\"*.ply\"))\n", "        print(f\"Found {len(self.cluster_files)} cluster files\")\n", "        \n", "    def __len__(self):\n", "        return len(self.cluster_files)\n", "    \n", "    def __getitem__(self, idx):\n", "        cluster_file = self.cluster_files[idx]\n", "        \n", "        # Load point cloud\n", "        pcd = o3d.io.read_point_cloud(str(cluster_file))\n", "        points = np.asarray(pcd.points)\n", "        \n", "        # Enhanced preprocessing for PointNet++\n", "        points = self.process_points_enhanced(points)\n", "        \n", "        # Load labels if available\n", "        if self.labels_dir:\n", "            label_file = self.labels_dir / f\"{cluster_file.stem}_labels.npy\"\n", "            if label_file.exists():\n", "                labels = np.load(label_file)\n", "                labels = self.process_labels(labels, len(points))\n", "            else:\n", "                labels = np.full(len(points), self.class_map['non-pile'], dtype=np.int64)  # default\n", "        else:\n", "            labels = np.full(len(points), -1, dtype=np.int64)  # unlabeled\n", "            \n", "        # Data augmentation\n", "        if self.augment:\n", "            points = self.augment_points(points)\n", "            \n", "        return {\n", "            'points': torch.FloatTensor(points).transpose(1, 0),  # [3, N]\n", "            'labels': torch.LongTensor(labels),\n", "            'cluster_id': cluster_file.stem\n", "        }\n", "    \n", "    def process_points_enhanced(self, points):\n", "        \"\"\"Enhanced point processing for PointNet++\"\"\"\n", "        # Remove outliers first\n", "        if len(points) > 100:\n", "            pcd = o3d.geometry.PointCloud()\n", "            pcd.points = o3d.utility.Vector3dVector(points)\n", "            pcd, _ = pcd.remove_statistical_outlier(nb_neighbors=20, std_ratio=2.0)\n", "            points = np.asarray(pcd.points)\n", "        \n", "        # Sample or pad points\n", "        if len(points) >= self.num_points:\n", "            # Use farthest point sampling for better coverage\n", "            indices = self.farthest_point_sampling_numpy(points, self.num_points)\n", "            points = points[indices]\n", "        else:\n", "            # Pad with jittered copies\n", "            n_repeats = (self.num_points // len(points)) + 1\n", "            repeated_points = np.tile(points, (n_repeats, 1))\n", "            \n", "            # Add small jitter to avoid identical points\n", "            jitter = np.random.normal(0, 0.01, repeated_points.shape)\n", "            repeated_points = repeated_points + jitter\n", "            \n", "            points = repeated_points[:self.num_points]\n", "            \n", "        # Normalize to unit sphere (better for PointNet++)\n", "        centroid = np.mean(points, axis=0)\n", "        points = points - centroid\n", "        max_dist = np.max(np.linalg.norm(points, axis=1))\n", "        if max_dist > 0:\n", "            points = points / max_dist\n", "            \n", "        return points\n", "    \n", "    def farthest_point_sampling_numpy(self, points, num_samples):\n", "        \"\"\"Numpy implementation of farthest point sampling\"\"\"\n", "        n_points = len(points)\n", "        selected = np.zeros(num_samples, dtype=np.int64)\n", "        distances = np.full(n_points, np.inf)\n", "        \n", "        # Start with random point\n", "        selected[0] = np.random.randint(0, n_points)\n", "        \n", "        for i in range(1, num_samples):\n", "            last_selected = selected[i-1]\n", "            # Calculate distances to last selected point\n", "            dists = np.linalg.norm(points - points[last_selected], axis=1)\n", "            # Update minimum distances\n", "            distances = np.minimum(distances, dists)\n", "            # Select farthest point\n", "            selected[i] = np.argmax(distances)\n", "            \n", "        return selected\n", "    \n", "    def process_labels(self, labels, target_length):\n", "        \"\"\"Process labels to match point count\"\"\"\n", "        if len(labels) >= target_length:\n", "            # Use same indices as point sampling\n", "            indices = self.farthest_point_sampling_numpy(\n", "                np.arange(len(labels)).reshape(-1, 1), target_length\n", "            )\n", "            return labels[indices]\n", "        else:\n", "            repeats = target_length // len(labels) + 1\n", "            labels = np.tile(labels, repeats)[:target_length]\n", "            return labels\n", "    \n", "    def augment_points(self, points):\n", "        \"\"\"Enhanced data augmentation for construction scenes\"\"\"\n", "        # Random rotation around Z-axis (vertical structures)\n", "        angle = np.random.uniform(0, 2 * np.pi)\n", "        cos_a, sin_a = np.cos(angle), np.sin(angle)\n", "        rotation = np.array([[cos_a, -sin_a, 0],\n", "                           [sin_a, cos_a, 0],\n", "                           [0, 0, 1]])\n", "        points = points @ rotation.T\n", "        \n", "        # Random scaling (realistic for construction tolerance)\n", "        scale = np.random.uniform(0.95, 1.05)\n", "        points = points * scale\n", "        \n", "        # Random jittering (sensor noise simulation)\n", "        noise = np.random.normal(0, 0.01, points.shape)\n", "        points = points + noise\n", "        \n", "        return points"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Training Functions\n", "\n", "Let's implement the training and validation functions for our PointNet++ model."]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["def train_epoch(model, dataloader, optimizer, criterion, device):\n", "    \"\"\"Train for one epoch with PointNet++\"\"\"\n", "    model.train()\n", "    running_loss = 0.0\n", "    correct = 0\n", "    total = 0\n", "    \n", "    for batch_idx, data in enumerate(dataloader):\n", "        points = data['points'].to(device)  # [B, 3, N]\n", "        labels = data['labels'].to(device)  # [B, N]\n", "        \n", "        optimizer.zero_grad()\n", "        \n", "        pred = model(points)  # [B, N, num_classes]\n", "        #pred = pred.view(-1, model.num_classes)\n", "        pred = pred.reshape(-1, model.num_classes)\n", "        labels = labels.view(-1)\n", "        \n", "        # Filter out invalid labels\n", "        valid_mask = labels >= 0\n", "        if valid_mask.sum() == 0:\n", "            continue\n", "            \n", "        pred = pred[valid_mask]\n", "        labels = labels[valid_mask]\n", "        \n", "        loss = criterion(pred, labels)\n", "        loss.backward()\n", "        optimizer.step()\n", "        \n", "        running_loss += loss.item()\n", "        _, predicted = torch.max(pred, 1)\n", "        total += labels.size(0)\n", "        correct += (predicted == labels).sum().item()\n", "        \n", "        if batch_idx % 10 == 0:\n", "            print(f'Batch {batch_idx}/{len(dataloader)}, Loss: {loss.item():.4f}')\n", "    \n", "    accuracy = 100. * correct / total if total > 0 else 0\n", "    avg_loss = running_loss / len(dataloader)\n", "    \n", "    return avg_loss, accuracy\n", "\n", "def validate_epoch(model, dataloader, criterion, device):\n", "    \"\"\"Validate for one epoch\"\"\"\n", "    model.eval()\n", "    running_loss = 0.0\n", "    correct = 0\n", "    total = 0\n", "    all_preds = []\n", "    all_labels = []\n", "    \n", "    with torch.no_grad():\n", "        for data in dataloader:\n", "            points = data['points'].to(device)\n", "            labels = data['labels'].to(device)\n", "            \n", "            pred = model(points)\n", "            #pred = pred.view(-1, model.num_classes)\n", "            pred = pred.reshape(-1, model.num_classes)\n", "            labels = labels.view(-1)\n", "            \n", "            # Filter out invalid labels\n", "            valid_mask = labels >= 0\n", "            if valid_mask.sum() == 0:\n", "                continue\n", "                \n", "            pred = pred[valid_mask]\n", "            labels = labels[valid_mask]\n", "            \n", "            loss = criterion(pred, labels)\n", "            \n", "            running_loss += loss.item()\n", "            _, predicted = torch.max(pred, 1)\n", "            total += labels.size(0)\n", "            correct += (predicted == labels).sum().item()\n", "            \n", "            all_preds.extend(predicted.cpu().numpy())\n", "            all_labels.extend(labels.cpu().numpy())\n", "    \n", "    accuracy = 100. * correct / total if total > 0 else 0\n", "    avg_loss = running_loss / len(dataloader)\n", "    \n", "    return avg_loss, accuracy, all_preds, all_labels"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Main Training Function\n", "\n", "Let's create the main training function that puts everything together."]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["def train_pointnet_plus_segmentation():\n", "    \"\"\"Main training function for PointNet++\"\"\"\n", "    \n", "    # Parameters\n", "    CLUSTER_DIR = \"labeled_clusters\"\n", "    LABELS_DIR = \"labeled_clusters\"\n", "    NUM_POINTS = 4096  # Increased for better performance\n", "    BATCH_SIZE = 4     # Reduced due to memory requirements\n", "    NUM_EPOCHS = 50    # Reduced for demonstration\n", "    LEARNING_RATE = 0.001\n", "    \n", "    print(f\"Training Parameters:\")\n", "    print(f\"- Number of points per cluster: {NUM_POINTS}\")\n", "    print(f\"- Batch size: {BATCH_SIZE}\")\n", "    print(f\"- Number of epochs: {NUM_EPOCHS}\")\n", "    print(f\"- Learning rate: {LEARNING_RATE}\")\n", "    \n", "    # Create dataset\n", "    dataset = PileClusterDataset(\n", "        cluster_dir=CLUSTER_DIR,\n", "        labels_dir=LABELS_DIR,\n", "        num_points=NUM_POINTS,\n", "        augment=True\n", "    )\n", "    \n", "    if len(dataset) == 0:\n", "        print(\"No data found. Please ensure cluster files exist in the specified directory.\")\n", "        return None, None\n", "    \n", "    # Split dataset\n", "    train_size = int(0.8 * len(dataset))\n", "    val_size = len(dataset) - train_size\n", "    train_dataset, val_dataset = torch.utils.data.random_split(dataset, [train_size, val_size])\n", "    \n", "    print(f\"Dataset split: {train_size} training, {val_size} validation\")\n", "    \n", "    # Create dataloaders\n", "    train_loader = DataLoader(train_dataset, batch_size=4, shuffle=True, num_workers=0)\n", "    val_loader = DataLoader(val_dataset, batch_size=4, shuffle=False, num_workers=0)\n", "    \n", "    # Initialize model\n", "    model = PointNetPlusPlusSegmentation(num_classes=dataset.num_classes).to(device)\n", "    print(f\"Model initialized with {dataset.num_classes} classes\")\n", "    \n", "    # Loss and optimizer with class weighting\n", "    class_weights = torch.tensor([1.0, 1.2, 1.1, 0.8]).to(device)  # Emphasize pile/support classes\n", "    criterion = nn.NLLLoss(weight=class_weights)\n", "    optimizer = optim.Adam(model.parameters(), lr=LEARNING_RATE, weight_decay=1e-4)\n", "    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=NUM_EPOCHS)\n", "    \n", "    # Training loop\n", "    train_losses = []\n", "    train_accuracies = []\n", "    val_losses = []\n", "    val_accuracies = []\n", "    \n", "    best_val_acc = 0.0\n", "    \n", "    print(\"\\nStarting training...\")\n", "    for epoch in range(NUM_EPOCHS):\n", "        print(f'\\nEpoch {epoch+1}/{NUM_EPOCHS}')\n", "        print('-' * 50)\n", "        \n", "        # Train\n", "        train_loss, train_acc = train_epoch(model, train_loader, optimizer, criterion, device)\n", "        train_losses.append(train_loss)\n", "        train_accuracies.append(train_acc)\n", "        \n", "        # Validate\n", "        val_loss, val_acc, val_preds, val_labels = validate_epoch(model, val_loader, criterion, device)\n", "        val_losses.append(val_loss)\n", "        val_accuracies.append(val_acc)\n", "        \n", "        scheduler.step()\n", "        \n", "        print(f'Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.2f}%')\n", "        print(f'Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.2f}%')\n", "        print(f'Learning Rate: {scheduler.get_last_lr()[0]:.6f}')\n", "        \n", "        # Save best model\n", "        if val_acc > best_val_acc:\n", "            best_val_acc = val_acc\n", "            torch.save({\n", "                'epoch': epoch,\n", "                'model_state_dict': model.state_dict(),\n", "                'optimizer_state_dict': optimizer.state_dict(),\n", "                'val_acc': val_acc,\n", "                'class_map': dataset.class_map,\n", "                'num_points': NUM_POINTS\n", "            }, 'best_pointnet_plus_pile_segmentation.pth')\n", "            print(f'New best model saved with val acc: {val_acc:.2f}%')\n", "    \n", "    return model, dataset.class_map"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [], "source": ["class PointwiseSegmentationDataset(Dataset):\n", "    \"\"\"Dataset for pointwise segmentation with enhanced preprocessing\"\"\"\n", "    \n", "    def __init__(self, cluster_dir, labels_dir=None, num_points=4096, augment=False):\n", "        self.cluster_dir = Path(cluster_dir)\n", "        self.labels_dir = Path(labels_dir) if labels_dir else None\n", "        self.num_points = num_points\n", "        self.augment = augment\n", "        \n", "        # Class mapping for construction elements\n", "        self.class_map = {\n", "            'pile': 0,\n", "            'support': 1, \n", "            'pallet': 2,\n", "            'non-pile': 3\n", "        }\n", "        self.num_classes = len(self.class_map)\n", "        \n", "        # Load cluster files\n", "        self.cluster_files = list(self.cluster_dir.glob(\"*.ply\"))\n", "        print(f\"Found {len(self.cluster_files)} cluster files\")\n", "        \n", "    def __len__(self):\n", "        return len(self.cluster_files)\n", "    \n", "    def __getitem__(self, idx):\n", "        cluster_file = self.cluster_files[idx]\n", "        \n", "        # Load point cloud\n", "        pcd = o3d.io.read_point_cloud(str(cluster_file))\n", "        points = np.asarray(pcd.points)\n", "        \n", "        # Enhanced preprocessing\n", "        points = self.process_points_enhanced(points)\n", "        \n", "        # Load labels if available\n", "        if self.labels_dir:\n", "            label_file = self.labels_dir / f\"{cluster_file.stem}_labels.npy\"\n", "            if label_file.exists():\n", "                labels = np.load(label_file)\n", "                labels = self.process_labels(labels, len(points))\n", "            else:\n", "                labels = np.full(len(points), self.class_map['non-pile'], dtype=np.int64)\n", "        else:\n", "            labels = np.full(len(points), -1, dtype=np.int64)\n", "            \n", "        # Data augmentation\n", "        if self.augment:\n", "            points = self.augment_points(points)\n", "            \n", "        return {\n", "            'points': torch.FloatTensor(points).transpose(1, 0),  # [3, N]\n", "            'labels': torch.LongTensor(labels),\n", "            'cluster_id': cluster_file.stem\n", "        }\n", "    \n", "    def process_points_enhanced(self, points):\n", "        \"\"\"Enhanced point processing for PointNet++\"\"\"\n", "        # Remove outliers first\n", "        if len(points) > 100:\n", "            pcd = o3d.geometry.PointCloud()\n", "            pcd.points = o3d.utility.Vector3dVector(points)\n", "            pcd, _ = pcd.remove_statistical_outlier(nb_neighbors=20, std_ratio=2.0)\n", "            points = np.asarray(pcd.points)\n", "        \n", "        # Sample or pad points\n", "        if len(points) >= self.num_points:\n", "            # Use farthest point sampling for better coverage\n", "            indices = self.farthest_point_sampling_numpy(points, self.num_points)\n", "            points = points[indices]\n", "        else:\n", "            # Pad with jittered copies\n", "            n_repeats = (self.num_points // len(points)) + 1\n", "            repeated_points = np.tile(points, (n_repeats, 1))\n", "            \n", "            # Add small jitter to avoid identical points\n", "            jitter = np.random.normal(0, 0.01, repeated_points.shape)\n", "            repeated_points = repeated_points + jitter\n", "            \n", "            points = repeated_points[:self.num_points]\n", "            \n", "        # Normalize to unit sphere (better for PointNet++)\n", "        centroid = np.mean(points, axis=0)\n", "        points = points - centroid\n", "        max_dist = np.max(np.linalg.norm(points, axis=1))\n", "        if max_dist > 0:\n", "            points = points / max_dist\n", "            \n", "        return points\n", "    \n", "    def farthest_point_sampling_numpy(self, points, num_samples):\n", "        \"\"\"Numpy implementation of farthest point sampling\"\"\"\n", "        n_points = len(points)\n", "        selected = np.zeros(num_samples, dtype=np.int64)\n", "        distances = np.full(n_points, np.inf)\n", "        \n", "        # Start with random point\n", "        selected[0] = np.random.randint(0, n_points)\n", "        \n", "        for i in range(1, num_samples):\n", "            last_selected = selected[i-1]\n", "            # Calculate distances to last selected point\n", "            dists = np.linalg.norm(points - points[last_selected], axis=1)\n", "            # Update minimum distances\n", "            distances = np.minimum(distances, dists)\n", "            # Select farthest point\n", "            selected[i] = np.argmax(distances)\n", "            \n", "        return selected\n", "    \n", "    def process_labels(self, labels, target_length):\n", "        \"\"\"Process labels to match point count\"\"\"\n", "        if len(labels) >= target_length:\n", "            # Use same indices as point sampling\n", "            indices = self.farthest_point_sampling_numpy(\n", "                np.arange(len(labels)).reshape(-1, 1), target_length\n", "            )\n", "            return labels[indices]\n", "        else:\n", "            repeats = target_length // len(labels) + 1\n", "            labels = np.tile(labels, repeats)[:target_length]\n", "            return labels\n", "    \n", "    def augment_points(self, points):\n", "        \"\"\"Enhanced data augmentation for construction scenes\"\"\"\n", "        # Random rotation around Z-axis (vertical structures)\n", "        angle = np.random.uniform(0, 2 * np.pi)\n", "        cos_a, sin_a = np.cos(angle), np.sin(angle)\n", "        rotation = np.array([[cos_a, -sin_a, 0],\n", "                           [sin_a, cos_a, 0],\n", "                           [0, 0, 1]])\n", "        points = points @ rotation.T\n", "        \n", "        # Random scaling (realistic for construction tolerance)\n", "        scale = np.random.uniform(0.95, 1.05)\n", "        points = points * scale\n", "        \n", "        # Random jittering (sensor noise simulation)\n", "        noise = np.random.normal(0, 0.01, points.shape)\n", "        points = points + noise\n", "        \n", "        return points"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'PATCH_SIZE' is not defined", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[36]\u001b[39m\u001b[32m, line 5\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;66;03m# Create dataset and examine class distribution\u001b[39;00m\n\u001b[32m      2\u001b[39m dataset = PointwiseSegmentationDataset(\n\u001b[32m      3\u001b[39m     cluster_dir=\u001b[33m\"\u001b[39m\u001b[33mlabeled_clusters\u001b[39m\u001b[33m\"\u001b[39m, \n\u001b[32m      4\u001b[39m     labels_dir=\u001b[33m\"\u001b[39m\u001b[33mlabeled_clusters\u001b[39m\u001b[33m\"\u001b[39m, \n\u001b[32m----> \u001b[39m\u001b[32m5\u001b[39m     patch_size=\u001b[43mPATCH_SIZE\u001b[49m, \n\u001b[32m      6\u001b[39m     pile_radius=PILE_RADIUS, \n\u001b[32m      7\u001b[39m     num_points=NUM_POINTS\n\u001b[32m      8\u001b[39m )\n\u001b[32m     10\u001b[39m \u001b[38;5;66;03m# Check class balance (pile vs non-pile) in first few samples\u001b[39;00m\n\u001b[32m     11\u001b[39m pile_ratios = []\n", "\u001b[31mNameError\u001b[39m: name 'PATCH_SIZE' is not defined"]}], "source": ["# Create dataset and examine class distribution\n", "dataset = PointwiseSegmentationDataset(\n", "    cluster_dir=\"labeled_clusters\", \n", "    labels_dir=\"labeled_clusters\", \n", "    patch_size=PATCH_SIZE, \n", "    pile_radius=PILE_RADIUS, \n", "    num_points=NUM_POINTS\n", ")\n", "\n", "# Check class balance (pile vs non-pile) in first few samples\n", "pile_ratios = []\n", "\n", "labels = np.zeros(dataset.num_points, dtype=np.int64)\n", "\n", "for i in range(min(10, len(dataset))):\n", "    _, labels = dataset[i]\n", "    labels = torch.tensor(labels, dtype=torch.float32)  # Ensure tensor and float\n", "    pile_ratio = (labels == 1).float().mean().item()\n", "    pile_ratios.append(pile_ratio)\n", "\n", "print(\"<PERSON><PERSON> (first 10 samples):\", pile_ratios)\n", "\n", "avg_pile_ratio = np.mean(pile_ratios)\n", "print(f\"Average pile point ratio across samples: {avg_pile_ratio:.3f}\")\n", "print(f\"Class distribution: {1-avg_pile_ratio:.3f} non-pile, {avg_pile_ratio:.3f} pile\")\n", "\n", "# Split dataset into train/val/test\n", "total_size = len(dataset)\n", "train_size = int(TRAIN_SPLIT * total_size)\n", "val_size = int(VAL_SPLIT * total_size)\n", "test_size = total_size - train_size - val_size\n", "\n", "train_dataset, val_dataset, test_dataset = torch.utils.data.random_split(\n", "    dataset, [train_size, val_size, test_size], \n", "    generator=torch.Generator().manual_seed(42)\n", ")\n", "\n", "# Create data loaders\n", "train_loader = DataLoader(train_dataset, batch_size=BATCH_SIZE, shuffle=True, num_workers=0)\n", "val_loader = DataLoader(val_dataset, batch_size=BATCH_SIZE, shuffle=False, num_workers=0)\n", "test_loader = DataLoader(test_dataset, batch_size=BATCH_SIZE, shuffle=False, num_workers=0)\n", "\n", "print(f\"Dataset splits:\")\n", "print(f\"  Train: {len(train_dataset)} scenes\")\n", "print(f\"  Validation: {len(val_dataset)} scenes\")\n", "print(f\"  Test: {len(test_dataset)} scenes\")\n", "\n", "if MLFLOW_AVAILABLE:\n", "    mlflow.log_metric(\"train_scenes\", len(train_dataset))\n", "    mlflow.log_metric(\"val_scenes\", len(val_dataset))\n", "    mlflow.log_metric(\"test_scenes\", len(test_dataset))\n", "    mlflow.log_metric(\"avg_pile_ratio\", avg_pile_ratio)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Visualization and Analysis Functions\n", "\n", "Let's create functions to visualize and analyze our results."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def visualize_segmentation(points, labels, confidence_scores, class_map, title=\"PointNet++ Segmentation\"):\n", "    \"\"\"Visualize segmentation results with confidence-based coloring\"\"\"\n", "    # Create color map\n", "    base_colors = {\n", "        0: np.array([1, 0, 0]),      # pile - red\n", "        1: np.array([0, 1, 0]),      # support - green  \n", "        2: np.array([0, 0, 1]),      # pallet - blue\n", "        3: np.array([0.5, 0.5, 0.5]) # non-pile - gray\n", "    }\n", "    \n", "    point_colors = []\n", "    for i, label in enumerate(labels):\n", "        base_color = base_colors.get(label, np.array([0, 0, 0]))\n", "        confidence = confidence_scores[i, label]\n", "        # Modulate color intensity by confidence\n", "        final_color = base_color * (0.3 + 0.7 * confidence)\n", "        point_colors.append(final_color)\n", "    \n", "    point_colors = np.array(point_colors)\n", "    \n", "    # Create point cloud\n", "    pcd = o3d.geometry.PointCloud()\n", "    pcd.points = o3d.utility.Vector3dVector(points)\n", "    pcd.colors = o3d.utility.Vector3dVector(point_colors)\n", "    \n", "    # Add coordinate frame for reference\n", "    coord_frame = o3d.geometry.TriangleMesh.create_coordinate_frame(size=1.0)\n", "    \n", "    # Visualize\n", "    o3d.visualization.draw_geometries([pcd, coord_frame], window_name=title)\n", "    \n", "    return pcd\n", "\n", "def analyze_pile_structure(points, labels, confidence_scores, class_map):\n", "    \"\"\"Analyze the structure of segmented pile\"\"\"\n", "    results = {}\n", "    \n", "    # Class distribution\n", "    unique_labels, counts = np.unique(labels, return_counts=True)\n", "    total_points = len(labels)\n", "    \n", "    class_distribution = {}\n", "    for label, count in zip(unique_labels, counts):\n", "        class_name = list(class_map.keys())[list(class_map.values()).index(label)]\n", "        percentage = (count / total_points) * 100\n", "        class_distribution[class_name] = {\n", "            'count': int(count),\n", "            'percentage': float(percentage)\n", "        }\n", "    \n", "    # Extract pile points specifically\n", "    pile_mask = labels == class_map['pile']\n", "    if pile_mask.sum() > 0:\n", "        pile_points = points[pile_mask]\n", "        pile_confidence = confidence_scores[pile_mask, class_map['pile']]\n", "        \n", "        # Pile geometry analysis\n", "        pile_centroid = np.mean(pile_points, axis=0)\n", "        pile_height = np.max(pile_points[:, 2]) - np.min(pile_points[:, 2])\n", "        \n", "        # Estimate pile diameter (using 2D projection)\n", "        pile_2d = pile_points[:, :2] - pile_centroid[:2]\n", "        pile_radius_estimates = np.linalg.norm(pile_2d, axis=1)\n", "        pile_diameter = 2 * np.percentile(pile_radius_estimates, 95)  # 95th percentile for robustness\n", "        \n", "        # Quality metrics\n", "        avg_confidence = np.mean(pile_confidence)\n", "        min_confidence = np.min(pile_confidence)\n", "        \n", "        results['pile_analysis'] = {\n", "            'centroid': pile_centroid.tolist(),\n", "            'height': float(pile_height),\n", "            'estimated_diameter': float(pile_diameter),\n", "            'avg_confidence': float(avg_confidence),\n", "            'min_confidence': float(min_confidence),\n", "            'quality_score': float(avg_confidence * (pile_mask.sum() / total_points))\n", "        }\n", "    else:\n", "        results['pile_analysis'] = None\n", "    \n", "    results['class_distribution'] = class_distribution\n", "    results['total_points'] = total_points\n", "    \n", "    return results"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Evaluation and Visualization Functions\n", "\n", "Let's create comprehensive evaluation functions including confusion matrix, IoU metrics, and point cloud visualizations."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 8.1 Confusion Matrix and IoU Calculation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def calculate_iou_per_class(y_true, y_pred, num_classes, class_names):\n", "    \"\"\"Calculate Intersection over Union (IoU) for each class\"\"\"\n", "    iou_scores = {}\n", "    \n", "    for class_idx in range(num_classes):\n", "        class_name = class_names[class_idx]\n", "        \n", "        # True positives, false positives, false negatives\n", "        tp = np.sum((y_true == class_idx) & (y_pred == class_idx))\n", "        fp = np.sum((y_true != class_idx) & (y_pred == class_idx))\n", "        fn = np.sum((y_true == class_idx) & (y_pred != class_idx))\n", "        \n", "        # Calculate IoU\n", "        if tp + fp + fn == 0:\n", "            iou = 0.0  # No instances of this class\n", "        else:\n", "            iou = tp / (tp + fp + fn)\n", "        \n", "        iou_scores[class_name] = {\n", "            'iou': float(iou),\n", "            'tp': int(tp),\n", "            'fp': int(fp),\n", "            'fn': int(fn)\n", "        }\n", "    \n", "    # Calculate mean IoU\n", "    mean_iou = np.mean([scores['iou'] for scores in iou_scores.values()])\n", "    \n", "    return iou_scores, mean_iou\n", "\n", "def plot_confusion_matrix_and_iou(y_true, y_pred, class_names, title=\"Model Evaluation\"):\n", "    \"\"\"Plot confusion matrix and display IoU metrics\"\"\"\n", "    # Create figure with subplots\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "    \n", "    # Plot confusion matrix\n", "    cm = confusion_matrix(y_true, y_pred)\n", "    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', \n", "                xticklabels=class_names, yticklabels=class_names, ax=ax1)\n", "    ax1.set_title('Confusion Matrix')\n", "    ax1.set_xlabel('Predicted')\n", "    ax1.set_ylabel('Actual')\n", "    \n", "    # Calculate and display IoU scores\n", "    iou_scores, mean_iou = calculate_iou_per_class(y_true, y_pred, len(class_names), class_names)\n", "    \n", "    # Plot IoU scores\n", "    classes = list(iou_scores.keys())\n", "    ious = [iou_scores[cls]['iou'] for cls in classes]\n", "    \n", "    bars = ax2.bar(classes, ious, color=['red', 'green', 'blue', 'gray'])\n", "    ax2.set_title(f'Per-Class IoU (Mean IoU: {mean_iou:.3f})')\n", "    ax2.set_ylabel('IoU Score')\n", "    ax2.set_ylim(0, 1)\n", "    \n", "    # Add value labels on bars\n", "    for bar, iou in zip(bars, ious):\n", "        height = bar.get_height()\n", "        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.01,\n", "                f'{iou:.3f}', ha='center', va='bottom')\n", "    \n", "    plt.xticks(rotation=45)\n", "    plt.tight_layout()\n", "    plt.suptitle(title, y=1.02)\n", "    plt.show()\n", "    \n", "    # Print detailed IoU information\n", "    print(\"\\nDetailed IoU Analysis:\")\n", "    print(\"-\" * 50)\n", "    for class_name, scores in iou_scores.items():\n", "        print(f\"{class_name:>10}: IoU={scores['iou']:.3f} (TP={scores['tp']}, FP={scores['fp']}, FN={scores['fn']})\")\n", "    print(f\"{'Mean IoU':>10}: {mean_iou:.3f}\")\n", "    \n", "    return iou_scores, mean_iou"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 8.2 Enhanced Point Cloud Visualization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def visualize_predictions_detailed(points, true_labels, pred_labels, confidence_scores, class_map, \n", "                                 cluster_id=\"unknown\", save_path=None):\n", "    \"\"\"Detailed visualization of predictions vs ground truth\"\"\"\n", "    \n", "    # Define colors for each class\n", "    class_colors = {\n", "        0: [1.0, 0.0, 0.0],    # pile - red\n", "        1: [0.0, 1.0, 0.0],    # support - green\n", "        2: [0.0, 0.0, 1.0],    # pallet - blue\n", "        3: [0.5, 0.5, 0.5]     # non-pile - gray\n", "    }\n", "    \n", "    # Create three point clouds: ground truth, predictions, and confidence-based\n", "    fig = plt.figure(figsize=(18, 6))\n", "    \n", "    # Ground Truth Visualization\n", "    pcd_gt = o3d.geometry.PointCloud()\n", "    pcd_gt.points = o3d.utility.Vector3dVector(points)\n", "    \n", "    gt_colors = np.array([class_colors.get(label, [0, 0, 0]) for label in true_labels])\n", "    pcd_gt.colors = o3d.utility.Vector3dVector(gt_colors)\n", "    \n", "    # Predictions Visualization\n", "    pcd_pred = o3d.geometry.PointCloud()\n", "    pcd_pred.points = o3d.utility.Vector3dVector(points)\n", "    \n", "    pred_colors = np.array([class_colors.get(label, [0, 0, 0]) for label in pred_labels])\n", "    pcd_pred.colors = o3d.utility.Vector3dVector(pred_colors)\n", "    \n", "    # Confidence-based Visualization\n", "    pcd_conf = o3d.geometry.PointCloud()\n", "    pcd_conf.points = o3d.utility.Vector3dVector(points)\n", "    \n", "    conf_colors = []\n", "    for i, (pred_label, conf_scores) in enumerate(zip(pred_labels, confidence_scores)):\n", "        base_color = np.array(class_colors.get(pred_label, [0, 0, 0]))\n", "        confidence = conf_scores[pred_label]\n", "        # Modulate color intensity by confidence (low confidence = darker)\n", "        final_color = base_color * (0.3 + 0.7 * confidence)\n", "        conf_colors.append(final_color)\n", "    \n", "    pcd_conf.colors = o3d.utility.Vector3dVector(np.array(conf_colors))\n", "    \n", "    # Display all three visualizations\n", "    print(f\"\\nVisualizing cluster: {cluster_id}\")\n", "    print(\"Close each window to proceed to the next visualization\")\n", "    \n", "    # Ground truth\n", "    print(\"\\n1. Ground Truth Labels\")\n", "    o3d.visualization.draw_geometries([pcd_gt], \n", "                                    window_name=f\"Ground Truth - {cluster_id}\")\n", "    \n", "    # Predictions\n", "    print(\"2. Model Predictions\")\n", "    o3d.visualization.draw_geometries([pcd_pred], \n", "                                    window_name=f\"Predictions - {cluster_id}\")\n", "    \n", "    # Confidence-based\n", "    print(\"3. Confidence-based Coloring (darker = lower confidence)\")\n", "    o3d.visualization.draw_geometries([pcd_conf], \n", "                                    window_name=f\"Confidence - {cluster_id}\")\n", "    \n", "    # Save point clouds if path provided\n", "    if save_path:\n", "        save_path = Path(save_path)\n", "        save_path.mkdir(exist_ok=True)\n", "        \n", "        o3d.io.write_point_cloud(str(save_path / f\"{cluster_id}_ground_truth.ply\"), pcd_gt)\n", "        o3d.io.write_point_cloud(str(save_path / f\"{cluster_id}_predictions.ply\"), pcd_pred)\n", "        o3d.io.write_point_cloud(str(save_path / f\"{cluster_id}_confidence.ply\"), pcd_conf)\n", "        print(f\"\\nSaved visualizations to: {save_path}\")\n", "    \n", "    return pcd_gt, pcd_pred, pcd_conf\n", "\n", "def create_side_by_side_comparison(points, true_labels, pred_labels, confidence_scores, \n", "                                 class_map, cluster_id=\"comparison\"):\n", "    \"\"\"Create a side-by-side comparison visualization\"\"\"\n", "    \n", "    class_colors = {\n", "        0: [1.0, 0.0, 0.0],    # pile - red\n", "        1: [0.0, 1.0, 0.0],    # support - green\n", "        2: [0.0, 0.0, 1.0],    # pallet - blue\n", "        3: [0.5, 0.5, 0.5]     # non-pile - gray\n", "    }\n", "    \n", "    # Create combined point cloud with side-by-side layout\n", "    offset = np.array([np.max(points[:, 0]) - np.min(points[:, 0]) + 2, 0, 0])\n", "    \n", "    # Ground truth on the left\n", "    points_gt = points.copy()\n", "    gt_colors = np.array([class_colors.get(label, [0, 0, 0]) for label in true_labels])\n", "    \n", "    # Predictions on the right\n", "    points_pred = points + offset\n", "    pred_colors = np.array([class_colors.get(label, [0, 0, 0]) for label in pred_labels])\n", "    \n", "    # Combine both point clouds\n", "    combined_points = np.vstack([points_gt, points_pred])\n", "    combined_colors = np.vstack([gt_colors, pred_colors])\n", "    \n", "    # Create combined point cloud\n", "    pcd_combined = o3d.geometry.PointCloud()\n", "    pcd_combined.points = o3d.utility.Vector3dVector(combined_points)\n", "    pcd_combined.colors = o3d.utility.Vector3dVector(combined_colors)\n", "    \n", "    # Add text labels (using coordinate frames as markers)\n", "    coord_gt = o3d.geometry.TriangleMesh.create_coordinate_frame(size=1.0)\n", "    coord_gt.translate([np.min(points_gt[:, 0]), np.min(points_gt[:, 1]), np.max(points_gt[:, 2]) + 1])\n", "    \n", "    coord_pred = o3d.geometry.TriangleMesh.create_coordinate_frame(size=1.0)\n", "    coord_pred.translate([np.min(points_pred[:, 0]), np.min(points_pred[:, 1]), np.max(points_pred[:, 2]) + 1])\n", "    \n", "    print(f\"\\nSide-by-side comparison for {cluster_id}:\")\n", "    print(\"Left: Ground Truth | Right: Predictions\")\n", "    \n", "    o3d.visualization.draw_geometries([pcd_combined, coord_gt, coord_pred], \n", "                                    window_name=f\"GT vs Predictions - {cluster_id}\")\n", "    \n", "    return pcd_combined"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 8.3 Error Analysis Visualization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def visualize_prediction_errors(points, true_labels, pred_labels, confidence_scores, \n", "                               class_map, cluster_id=\"error_analysis\"):\n", "    \"\"\"Visualize prediction errors with different colors for different types of mistakes\"\"\"\n", "    \n", "    # Create error mask\n", "    correct_mask = true_labels == pred_labels\n", "    error_mask = ~correct_mask\n", "    \n", "    # Color scheme for errors\n", "    error_colors = np.zeros((len(points), 3))\n", "    \n", "    # Correct predictions - green\n", "    error_colors[correct_mask] = [0, 1, 0]\n", "    \n", "    # Different error types with different colors\n", "    for i in range(len(points)):\n", "        if error_mask[i]:\n", "            true_label = true_labels[i]\n", "            pred_label = pred_labels[i]\n", "            confidence = confidence_scores[i, pred_label]\n", "            \n", "            # Color based on error type and confidence\n", "            if true_label == 0 and pred_label != 0:  # Missed pile\n", "                error_colors[i] = [1, 0, 0]  # Red\n", "            elif true_label != 0 and pred_label == 0:  # False pile detection\n", "                error_colors[i] = [1, 0.5, 0]  # Orange\n", "            else:  # Other misclassifications\n", "                error_colors[i] = [1, 1, 0]  # Yellow\n", "            \n", "            # Modulate intensity by confidence (low confidence = darker)\n", "            error_colors[i] = error_colors[i] * (0.3 + 0.7 * confidence)\n", "    \n", "    # Create error visualization point cloud\n", "    pcd_errors = o3d.geometry.PointCloud()\n", "    pcd_errors.points = o3d.utility.Vector3dVector(points)\n", "    pcd_errors.colors = o3d.utility.Vector3dVector(error_colors)\n", "    \n", "    # Calculate error statistics\n", "    total_points = len(points)\n", "    correct_points = np.sum(correct_mask)\n", "    error_points = np.sum(error_mask)\n", "    accuracy = correct_points / total_points\n", "    \n", "    # Pile-specific errors\n", "    pile_true = true_labels == 0\n", "    pile_pred = pred_labels == 0\n", "    \n", "    missed_piles = np.sum(pile_true & ~pile_pred)\n", "    false_piles = np.sum(~pile_true & pile_pred)\n", "    correct_piles = np.sum(pile_true & pile_pred)\n", "    \n", "    print(f\"\\nError Analysis for {cluster_id}:\")\n", "    print(\"-\" * 40)\n", "    print(f\"Overall Accuracy: {accuracy:.3f} ({correct_points}/{total_points})\")\n", "    print(f\"Total Errors: {error_points} points\")\n", "    print(f\"\\nPile Detection Errors:\")\n", "    print(f\"  Miss<PERSON> Piles (Red): {missed_piles} points\")\n", "    print(f\"  <PERSON>als<PERSON> (Orange): {false_piles} points\")\n", "    print(f\"  Correct Piles: {correct_piles} points\")\n", "    print(f\"\\nColor Legend:\")\n", "    print(f\"  Green: Correct predictions\")\n", "    print(f\"  Red: Missed pile points\")\n", "    print(f\"  Orange: False pile detections\")\n", "    print(f\"  Yellow: Other misclassifications\")\n", "    print(f\"  Darker colors: Lower confidence\")\n", "    \n", "    o3d.visualization.draw_geometries([pcd_errors], \n", "                                    window_name=f\"Error Analysis - {cluster_id}\")\n", "    \n", "    return pcd_errors, {\n", "        'accuracy': accuracy,\n", "        'total_errors': error_points,\n", "        'missed_piles': missed_piles,\n", "        'false_piles': false_piles,\n", "        'correct_piles': correct_piles\n", "    }"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. Inference Function\n", "\n", "Let's create a function to perform inference on new point cloud clusters."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def segment_cluster_pointnet_plus(model, cluster_file, class_map, num_points=4096, device='cpu'):\n", "    \"\"\"Segment a single cluster with PointNet++\"\"\"\n", "    model.eval()\n", "    \n", "    # Load point cloud\n", "    pcd = o3d.io.read_point_cloud(cluster_file)\n", "    original_points = np.asarray(pcd.points)\n", "    \n", "    print(f\"Loaded cluster with {len(original_points)} points\")\n", "    \n", "    # Process points with enhanced preprocessing\n", "    dataset = PileClusterDataset(\".\", num_points=num_points)\n", "    processed_points = dataset.process_points_enhanced(original_points)\n", "    \n", "    # Convert to tensor\n", "    points_tensor = torch.FloatTensor(processed_points).transpose(1, 0).unsqueeze(0).to(device)\n", "    \n", "    with torch.no_grad():\n", "        pred = model(points_tensor)  # [1, N, num_classes]\n", "        pred = pred.squeeze(0)  # [N, num_classes]\n", "        predicted_labels = torch.argmax(pred, dim=1).cpu().numpy()\n", "        confidence_scores = torch.softmax(pred, dim=1).cpu().numpy()\n", "    \n", "    print(f\"Segmentation complete. Processed {len(processed_points)} points\")\n", "    \n", "    return original_points, processed_points, predicted_labels, confidence_scores"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. Example <PERSON> and De<PERSON>\n", "\n", "Let's create a demonstration of how to use the PointNet++ model."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def demo_pointnet_plus_segmentation():\n", "    \"\"\"Demonstration of PointNet++ pile segmentation\"\"\"\n", "    print(\"=== POINTNET++ PILE SEGMENTATION DEMO ===\")\n", "    print(\"Enhanced with hierarchical feature learning and better geometric understanding\")\n", "    \n", "    # Check if we have data for training\n", "    cluster_dir = Path(\"labeled_clusters\")\n", "    labels_dir = cluster_dir  # both clusters and labels are now in the same directory\n", "    \n", "    if not cluster_dir.exists():\n", "        print(f\"Cluster directory '{cluster_dir}' not found.\")\n", "        print(\"Please run DBSCAN clustering first to generate cluster files.\")\n", "        return\n", "    \n", "    cluster_files = list(cluster_dir.glob(\"*.ply\"))\n", "    if len(cluster_files) == 0:\n", "        print(\"No cluster files found. Please ensure .ply files exist in the cluster directory.\")\n", "        return\n", "    \n", "    print(f\"Found {len(cluster_files)} cluster files\")\n", "    \n", "    # Check if we have labeled data for training\n", "    if labels_dir.exists() and len(list(labels_dir.glob(\"*.npy\"))) > 0:\n", "        print(\"Found labeled data - training PointNet++ model...\")\n", "        model, class_map = train_pointnet_plus_segmentation()\n", "        \n", "        if model is None:\n", "            print(\"Training failed. Please check your data.\")\n", "            return\n", "            \n", "    else:\n", "        print(\"No labeled data found.\")\n", "        print(\"For demonstration, we'll create a model with default classes.\")\n", "        \n", "        # Create model with default classes\n", "        class_map = {'pile': 0, 'support': 1, 'pallet': 2, 'non-pile': 3}\n", "        model = PointNetPlusPlusSegmentation(num_classes=len(class_map)).to(device)\n", "        print(\"Created PointNet++ model for demonstration\")\n", "    \n", "    # Demonstrate inference on a sample cluster\n", "    sample_cluster = cluster_files[0]\n", "    print(f\"\\nDemonstrating inference on: {sample_cluster.name}\")\n", "    \n", "    try:\n", "        original_points, processed_points, predicted_labels, confidence_scores = segment_cluster_pointnet_plus(\n", "            model, str(sample_cluster), class_map, device=device\n", "        )\n", "        \n", "        # Analyze results\n", "        analysis = analyze_pile_structure(processed_points, predicted_labels, confidence_scores, class_map)\n", "        \n", "        print(\"\\n=== ANALYSIS RESULTS ===\")\n", "        print(f\"Total points processed: {analysis['total_points']}\")\n", "        \n", "        print(\"\\nClass distribution:\")\n", "        for class_name, data in analysis['class_distribution'].items():\n", "            print(f\"  {class_name}: {data['count']} points ({data['percentage']:.1f}%)\")\n", "        \n", "        if analysis['pile_analysis']:\n", "            pile_info = analysis['pile_analysis']\n", "            print(\"\\nPile analysis:\")\n", "            print(f\"  Centroid: ({pile_info['centroid'][0]:.2f}, {pile_info['centroid'][1]:.2f}, {pile_info['centroid'][2]:.2f})\")\n", "            print(f\"  Height: {pile_info['height']:.2f}m\")\n", "            print(f\"  Estimated diameter: {pile_info['estimated_diameter']:.2f}m\")\n", "            print(f\"  Average confidence: {pile_info['avg_confidence']:.3f}\")\n", "            print(f\"  Quality score: {pile_info['quality_score']:.3f}\")\n", "        else:\n", "            print(\"\\nNo pile detected in this cluster\")\n", "        \n", "        # Visualize results\n", "        print(\"\\nVisualizing segmentation results...\")\n", "        visualize_segmentation(processed_points, predicted_labels, confidence_scores, class_map, \n", "                             title=f\"PointNet++ Segmentation - {sample_cluster.name}\")\n", "        \n", "    except Exception as e:\n", "        print(f\"Error during inference: {str(e)}\")\n", "        print(\"This is expected if no trained model is available.\")\n", "    \n", "    print(\"\\n=== DEMO COMPLETE ===\")\n", "    print(\"Key features of PointNet++:\")\n", "    print(\"- Hierarchical feature learning\")\n", "    print(\"- Better handling of point density variations\")\n", "    print(\"- Improved geometric understanding\")\n", "    print(\"- Enhanced preprocessing with FPS sampling\")\n", "    print(\"- Comprehensive pile analysis and reporting\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 10. Enhanced Evaluation Demo\n", "\n", "Let's create a comprehensive evaluation function that demonstrates all the visualization capabilities."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def comprehensive_evaluation_demo():\n", "    \"\"\"Comprehensive evaluation with confusion matrix, IoU, and detailed visualizations\"\"\"\n", "    print(\"=== COMPREHENSIVE POINTNET++ EVALUATION DEMO ===\")\n", "    \n", "    # Check for data\n", "    cluster_dir = Path(\"labeled_clusters\")\n", "    labels_dir = cluster_dir\n", "    \n", "    if not cluster_dir.exists() or len(list(cluster_dir.glob(\"*.ply\"))) == 0:\n", "        print(\"No cluster data found. Please run DBSCAN clustering first.\")\n", "        return\n", "    \n", "    if not labels_dir.exists() or len(list(labels_dir.glob(\"*.npy\"))) == 0:\n", "        print(\"No ground truth labels found. Creating synthetic data for demonstration...\")\n", "        \n", "        # Create synthetic evaluation data\n", "        cluster_files = list(cluster_dir.glob(\"*.ply\"))[:3]  # Use first 3 clusters\n", "        \n", "        for cluster_file in cluster_files:\n", "            # Load point cloud\n", "            pcd = o3d.io.read_point_cloud(str(cluster_file))\n", "            points = np.asarray(pcd.points)\n", "            \n", "            # Create synthetic labels (for demonstration)\n", "            num_points = min(len(points), 4096)\n", "            synthetic_labels = np.random.choice([0, 1, 2, 3], size=num_points, p=[0.4, 0.2, 0.2, 0.2])\n", "            \n", "            # Save synthetic labels\n", "            labels_dir.mkdir(exist_ok=True)\n", "            np.save(labels_dir / f\"{cluster_file.stem}_labels.npy\", synthetic_labels)\n", "        \n", "        print(f\"Created synthetic labels for {len(cluster_files)} clusters\")\n", "    \n", "    # Load or create model\n", "    class_map = {'pile': 0, 'support': 1, 'pallet': 2, 'non-pile': 3}\n", "    model = PointNetPlusPlusSegmentation(num_classes=len(class_map)).to(device)\n", "    \n", "    # Get clusters with labels\n", "    labeled_clusters = []\n", "    for label_file in labels_dir.glob(\"*.npy\"):\n", "        cluster_name = label_file.stem.replace(\"_labels\", \"\")\n", "        cluster_file = cluster_dir / f\"{cluster_name}.ply\"\n", "        if cluster_file.exists():\n", "            labeled_clusters.append((cluster_file, label_file))\n", "    \n", "    if not labeled_clusters:\n", "        print(\"No matching cluster and label files found.\")\n", "        return\n", "    \n", "    print(f\"Found {len(labeled_clusters)} clusters with labels\")\n", "    \n", "    # Evaluate on all labeled clusters\n", "    all_true_labels = []\n", "    all_pred_labels = []\n", "    all_confidences = []\n", "    \n", "    for i, (cluster_file, label_file) in enumerate(labeled_clusters[:3]):  # Limit to 3 for demo\n", "        print(f\"\\nEvaluating cluster {i+1}/{min(3, len(labeled_clusters))}: {cluster_file.name}\")\n", "        \n", "        try:\n", "            # Load and process data\n", "            original_points, processed_points, predicted_labels, confidence_scores = segment_cluster_pointnet_plus(\n", "                model, str(cluster_file), class_map, device=device\n", "            )\n", "            \n", "            # Load ground truth\n", "            true_labels = np.load(label_file)\n", "            \n", "            # Ensure same length\n", "            if len(true_labels) != len(processed_points):\n", "                dataset = PileClusterDataset(\".\", num_points=len(processed_points))\n", "                true_labels = dataset.process_labels(true_labels, len(processed_points))\n", "            \n", "            # Accumulate for overall evaluation\n", "            all_true_labels.extend(true_labels)\n", "            all_pred_labels.extend(predicted_labels)\n", "            all_confidences.extend(confidence_scores)\n", "            \n", "            # Individual cluster evaluation\n", "            print(f\"\\n--- Cluster {cluster_file.stem} Evaluation ---\")\n", "            class_names = list(class_map.keys())\n", "            \n", "            # Plot confusion matrix and IoU for this cluster\n", "            iou_scores, mean_iou = plot_confusion_matrix_and_iou(\n", "                true_labels, predicted_labels, class_names,\n", "                title=f\"Cluster {cluster_file.stem} Evaluation\"\n", "            )\n", "            \n", "            # Detailed visualizations\n", "            print(\"\\nShowing detailed visualizations (close windows to continue)...\")\n", "            \n", "            # Side-by-side comparison\n", "            create_side_by_side_comparison(\n", "                processed_points, true_labels, predicted_labels, confidence_scores,\n", "                class_map, cluster_id=cluster_file.stem\n", "            )\n", "            \n", "            # Error analysis\n", "            error_pcd, error_stats = visualize_prediction_errors(\n", "                processed_points, true_labels, predicted_labels, confidence_scores,\n", "                class_map, cluster_id=cluster_file.stem\n", "            )\n", "            \n", "        except Exception as e:\n", "            print(f\"Error processing {cluster_file.name}: {str(e)}\")\n", "            continue\n", "    \n", "    # Overall evaluation across all clusters\n", "    if all_true_labels and all_pred_labels:\n", "        print(\"\\n\" + \"=\"*60)\n", "        print(\"OVERALL EVALUATION ACROSS ALL CLUSTERS\")\n", "        print(\"=\"*60)\n", "        \n", "        all_true_labels = np.array(all_true_labels)\n", "        all_pred_labels = np.array(all_pred_labels)\n", "        all_confidences = np.array(all_confidences)\n", "        \n", "        # Overall confusion matrix and IoU\n", "        class_names = list(class_map.keys())\n", "        overall_iou_scores, overall_mean_iou = plot_confusion_matrix_and_iou(\n", "            all_true_labels, all_pred_labels, class_names,\n", "            title=\"Overall Performance Across All Clusters\"\n", "        )\n", "        \n", "        # Overall statistics\n", "        overall_accuracy = np.mean(all_true_labels == all_pred_labels)\n", "        print(f\"\\nOverall Statistics:\")\n", "        print(f\"  Total points evaluated: {len(all_true_labels)}\")\n", "        print(f\"  Overall accuracy: {overall_accuracy:.3f}\")\n", "        print(f\"  Overall mean IoU: {overall_mean_iou:.3f}\")\n", "        \n", "        # Per-class statistics\n", "        print(f\"\\nPer-class performance:\")\n", "        for class_name, scores in overall_iou_scores.items():\n", "            class_accuracy = scores['tp'] / (scores['tp'] + scores['fn']) if (scores['tp'] + scores['fn']) > 0 else 0\n", "            print(f\"  {class_name}: IoU={scores['iou']:.3f}, Accuracy={class_accuracy:.3f}\")\n", "    \n", "    print(\"\\n=== EVALUATION DEMO COMPLETE ===\")\n", "    print(\"\\nKey evaluation features demonstrated:\")\n", "    print(\"- Confusion matrix visualization\")\n", "    print(\"- Per-class IoU calculation\")\n", "    print(\"- Side-by-side GT vs Prediction comparison\")\n", "    print(\"- Error analysis with color-coded mistakes\")\n", "    print(\"- Overall performance across multiple clusters\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "\n", "cluster_dir = Path(\"labeled_clusters\")\n", "npy_files = list(cluster_dir.glob(\"*.npy\"))\n", "print(f\"Found {len(npy_files)} .npy label files\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 11. Run the Demos\n", "\n", "Execute the demonstrations to see PointNet++ in action."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Run the demonstration\n", "demo_pointnet_plus_segmentation()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Run the comprehensive evaluation demo\n", "# This includes confusion matrix, IoU metrics, and detailed visualizations\n", "comprehensive_evaluation_demo()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 12. Custom Evaluation Functions\n", "\n", "You can also run individual evaluation functions on your own data:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Example: Evaluate a specific cluster if you have the data\n", "# Uncomment and modify the paths below to use with your data\n", "\n", "# cluster_file = \"path/to/your/cluster.ply\"\n", "# label_file = \"path/to/your/labels.npy\"\n", "# \n", "# if Path(cluster_file).exists() and Path(label_file).exists():\n", "#     # Load data\n", "#     pcd = o3d.io.read_point_cloud(cluster_file)\n", "#     points = np.asarray(pcd.points)\n", "#     true_labels = np.load(label_file)\n", "#     \n", "#     # Create model and get predictions\n", "#     class_map = {'pile': 0, 'support': 1, 'pallet': 2, 'non-pile': 3}\n", "#     model = PointNetPlusPlusSegmentation(num_classes=len(class_map)).to(device)\n", "#     \n", "#     # Get predictions\n", "#     original_points, processed_points, predicted_labels, confidence_scores = segment_cluster_pointnet_plus(\n", "#         model, cluster_file, class_map, device=device\n", "#     )\n", "#     \n", "#     # Ensure same length\n", "#     if len(true_labels) != len(processed_points):\n", "#         dataset = PileClusterDataset(\".\", num_points=len(processed_points))\n", "#         true_labels = dataset.process_labels(true_labels, len(processed_points))\n", "#     \n", "#     # Run evaluations\n", "#     class_names = list(class_map.keys())\n", "#     \n", "#     # Confusion matrix and IoU\n", "#     iou_scores, mean_iou = plot_confusion_matrix_and_iou(\n", "#         true_labels, predicted_labels, class_names, title=\"Custom Evaluation\"\n", "#     )\n", "#     \n", "#     # Detailed visualizations\n", "#     visualize_predictions_detailed(\n", "#         processed_points, true_labels, predicted_labels, confidence_scores,\n", "#         class_map, cluster_id=\"custom\"\n", "#     )\n", "#     \n", "#     # Error analysis\n", "#     error_pcd, error_stats = visualize_prediction_errors(\n", "#         processed_points, true_labels, predicted_labels, confidence_scores,\n", "#         class_map, cluster_id=\"custom\"\n", "#     )\n", "\n", "print(\"Custom evaluation template ready - uncomment and modify paths to use with your data\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Summary\n", "\n", "This notebook demonstrated the implementation of **PointNet++** for semantic segmentation of construction pile clusters. Key learning points:\n", "\n", "### Architecture Components:\n", "1. **Farthest Point Sampling**: Selects representative points for hierarchical processing\n", "2. **Ball Query**: Groups neighboring points within a radius\n", "3. **Set Abstraction**: Reduces points while extracting features\n", "4. **Feature Propagation**: Upsamples features back to original resolution\n", "\n", "### Advantages over PointNet:\n", "- **Hierarchical processing**: Captures both local and global features\n", "- **Better geometric understanding**: Handles varying point densities\n", "- **Improved accuracy**: Especially for complex geometric structures\n", "\n", "### Applications:\n", "- Construction pile detection and classification\n", "- Quality assessment of foundation structures\n", "- As-built vs as-planned verification\n", "\n", "The implementation provides a solid foundation for understanding and applying PointNet++ to real-world construction monitoring tasks."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}