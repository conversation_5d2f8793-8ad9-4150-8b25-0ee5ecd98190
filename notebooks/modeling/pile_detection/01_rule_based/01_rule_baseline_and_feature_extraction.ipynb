# Import required libraries
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.cluster import DBSCAN
from sklearn.metrics import classification_report, confusion_matrix
import json
from pathlib import Path
from datetime import datetime
import warnings
import pickle

warnings.filterwarnings('ignore')

print("Libraries imported successfully")

from pathlib import Path
import pickle, json

print("Loading prepared patch data...")

try:
    #TODO : fix path
    base_dir = "../../00_data_preprocessing/"
    ml_patch_data_dir= "../../00_data_preprocessing/output/ml_patch_data"
    output_dir_file = Path(f"{base_dir}/output_dir.txt")
    output_dir = Path(output_dir_file.read_text().strip()).resolve()
    print(f"Loading data from: {output_dir}")

    datasets = {
        split: {
            'patches': pickle.load(open(ml_patch_data_dir / f"{split}_patches.pkl", 'rb')),
            'metadata': json.load(open(ml_patch_data_dir / f"{split}_metadata.json"))
        }
        for split in ['train', 'val', 'test']
    }

    for split, data in datasets.items():
        print(f"  {split.capitalize()}: {len(data['patches'])} patches")

    config = json.load(open(output_dir / "config.json"))
    print(f"Configuration: {config['parameters']}")

except FileNotFoundError as e:
    print(f"Error: {e}\nPlease run the patch extraction notebook first.")
    raise

print("Reconstructing combined dataset for analysis...")

all_patches = []
all_metadata = []

for split_name, split_data in datasets.items():
    patches = split_data['patches']
    metadata = split_data['metadata']
    
    for i, meta in enumerate(metadata):
        meta['split'] = split_name
        all_metadata.append(meta)
        all_patches.append(patches[i])

positive_patches = []
negative_patches = []
positive_info = []
negative_info = []

for patch, meta in zip(all_patches, all_metadata):
    if meta.get('label', 0) == 1 or meta.get('patch_type') == 'positive':
        positive_patches.append(patch)
        positive_info.append(meta)
    else:
        negative_patches.append(patch)
        negative_info.append(meta)

print("Data reconstruction complete:")
print(f"  Positive patches: {len(positive_patches)}")
print(f"  Negative patches: {len(negative_patches)}")
print(f"  Train/Val/Test: {len(datasets['train']['patches'])}/"
      f"{len(datasets['val']['patches'])}/"
      f"{len(datasets['test']['patches'])}")


# Extract pile coordinates if available
pile_coordinates = []
for meta in positive_info:
    if 'center_location' in meta:
        pile_coordinates.append(meta['center_location'])

pile_coordinates = np.array(pile_coordinates)
print(f"  Extracted {len(pile_coordinates)} pile coordinate references")


def extract_geometric_features(patch_points):
    if len(patch_points) == 0:
        return {}
    
    x, y, z = patch_points[:, 0], patch_points[:, 1], patch_points[:, 2]
    r = np.linalg.norm(patch_points[:, :2], axis=1)
    
    x_extent = x.max() - x.min()
    y_extent = y.max() - y.min()
    footprint_area = x_extent * y_extent
    height_range = z.max() - z.min()
    
    return {
        'num_points': len(patch_points),
        'height_range': height_range,
        'footprint_area': footprint_area,
        'point_density': len(patch_points) / footprint_area if footprint_area > 1e-6 else 0,
        'max_radial_distance': r.max(),
        'height_to_width_ratio': height_range / max(r.max(), 1e-6),
        'aspect_ratio_xy': x_extent / max(y_extent, 1e-6),
    }


def extract_confidence_features(metadata):
    """Extract features from pile metadata (confidence, source, etc.)"""
    features = {}
    
    # Confidence level encoding
    confidence_map = {'high': 3, 'medium': 2, 'low': 1, 'unknown': 0}
    features['confidence_score'] = confidence_map.get(metadata.get('confidence', 'unknown'), 0)
    
    # Source encoding
    source_map = {'matched': 3, 'ifc_only': 2, 'kml_only': 1, 'unknown': 0}
    features['source_score'] = source_map.get(metadata.get('source', 'unknown'), 0)
    
    # Extraction metadata
    features['radius_used'] = metadata.get('radius_used', 5.0)
    features['original_num_points'] = metadata.get('num_points', 0)
    
    return features

print("Feature extraction function defined")

# Extract features from positive patches (pile locations)
print("Extracting enhanced features from positive patches...")

positive_features = []
for i, (patch, meta) in enumerate(zip(positive_patches, positive_info)):
    # Geometric features
    geo_features = extract_geometric_features(patch)
    
    # Metadata features
    conf_features = extract_confidence_features(meta)
    
    # Combine all features
    features = {**geo_features, **conf_features}
    features['patch_type'] = 'positive'
    features['patch_index'] = i
    features['split'] = meta.get('split', 'unknown')
    
    positive_features.append(features)
    
    if (i + 1) % 100 == 0:
        print(f"  Processed {i + 1} positive patches...")

print(f"Completed feature extraction for {len(positive_features)} positive patches")

# Extract features from negative patches (non-pile locations)
print("Extracting enhanced features from negative patches...")

negative_features = []
for i, (patch, meta) in enumerate(zip(negative_patches, negative_info)):
    # Geometric features
    geo_features = extract_geometric_features(patch)
    
    # For negative patches, add dummy confidence features
    conf_features = {
        'confidence_score': 0,
        'source_score': 0,
        'radius_used': meta.get('radius_used', 5.0),
        'original_num_points': meta.get('num_points', 0)
    }
    
    # Combine all features
    features = {**geo_features, **conf_features}
    features['patch_type'] = 'negative'
    features['patch_index'] = i
    features['split'] = meta.get('split', 'unknown')
    
    negative_features.append(features)
    
    if (i + 1) % 100 == 0:
        print(f"  Processed {i + 1} negative patches...")

print(f"Completed enhanced feature extraction for {len(negative_features)} negative patches")


print("Creating enhanced feature DataFrame...")

# Combine and create DataFrame
feature_df = pd.DataFrame(positive_features + negative_features)

# Add derived columns
feature_df['is_pile'] = feature_df['patch_type'] == 'positive'
feature_df['confidence_category'] = feature_df['confidence_score'].map({3: 'high', 2: 'medium', 1: 'low', 0: 'none'})

# Print summary
print(f"Feature DataFrame created: {len(feature_df)} rows, {feature_df.shape[1]} columns")
print("Feature count (excluding meta):", len([c for c in feature_df.columns if c not in ['patch_type', 'patch_index', 'split', 'is_pile']]))

print("\nDataset distribution (by split and type):")
print(feature_df.pivot_table(index='patch_type', columns='split', aggfunc='size', fill_value=0))

print("\nSample features:")
cols_to_show = ['patch_type', 'confidence_category'] + feature_df.select_dtypes(include=[np.number]).columns[:10].tolist()
print(feature_df[cols_to_show].head())

# Compare features between positive and negative patches
print("Analyzing feature differences between pile and non-pile patches...")

# Separate positive and negative features
positive_df = feature_df[feature_df['patch_type'] == 'positive']
negative_df = feature_df[feature_df['patch_type'] == 'negative']

print(f"\nPositive patches: {len(positive_df)}")
print(f"Negative patches: {len(negative_df)}")

# Select numeric columns for comparison
numeric_columns = feature_df.select_dtypes(include=[np.number]).columns
numeric_columns = [col for col in numeric_columns if col not in ['patch_index']]

print(f"\nNumeric features for comparison: {len(numeric_columns)}")
print(numeric_columns)

# Calculate summary statistics for each feature
print("Comparing feature stats (mean ± std)...")

summary = []
for f in numeric_columns:
    pos, neg = positive_df[f].dropna(), negative_df[f].dropna()
    if not pos.empty and not neg.empty:
        stats = {
            'feature': f,
            'pos_mean': pos.mean(), 'neg_mean': neg.mean(),
            'pos_std': pos.std(), 'neg_std': neg.std(),
            'diff': pos.mean() - neg.mean()
        }
        summary.append(stats)
        print(f"{f:20s}: {stats['pos_mean']:.2f} ± {stats['pos_std']:.2f} | "
              f"{stats['neg_mean']:.2f} ± {stats['neg_std']:.2f} | Δ={stats['diff']:.2f}")

comparison_df = pd.DataFrame(summary)
print(f"\nFeature comparison completed for {len(comparison_df)} features")

def classify_pile_with_rules(features, debug=False):
    """Rule-based classification TUNED for your data"""
    score = 0
    rules = {}
    
    # Based on your data analysis - REVERSED logic!
    rules['lower_density'] = features.get('point_density', 0) < 0.7        # Piles are less dense
    rules['compact_footprint'] = features.get('max_radial_distance', 0) > 4.7  # Piles are more spread
    rules['moderate_height'] = 1.5 < features.get('height_range', 0) < 3.0     # Piles in height range
    rules['high_confidence'] = features.get('confidence_score', 0) >= 2        # Use confidence
    
    # Scoring - count positive rules
    score = sum([rules['lower_density'], rules['compact_footprint'], 
                rules['moderate_height'], rules['high_confidence']])
    
    is_pile = score >= 2  # Need at least 2 rules
    
    rules.update({
        'total_score': score,
        'classification': 'pile' if is_pile else 'non-pile'
    })
    
    return is_pile, rules

print("Applying rule-based classification...")

def apply_rules(row):
    is_pile, details = classify_pile_with_rules(row.to_dict())
    return pd.Series({
        'predicted_pile': is_pile,
        'rule_details': details  # Optional: if you want to inspect rule breakdown
    })

result = feature_df.apply(apply_rules, axis=1)
feature_df = pd.concat([feature_df, result], axis=1)

print(f"Classification completed for {len(feature_df)} patches")

from sklearn.metrics import confusion_matrix, classification_report

print("Evaluating classification performance...")

# Ensure binary values
true_labels = feature_df['true_pile'].astype(int)
predicted_labels = feature_df['predicted_pile'].astype(int)

# Confirm
assert isinstance(true_labels, pd.Series)
assert isinstance(predicted_labels, pd.Series)
assert set(true_labels.unique()).issubset({0, 1})
assert set(predicted_labels.unique()).issubset({0, 1})

# Confusion matrix
cm = confusion_matrix(true_labels, predicted_labels, labels=[0, 1])
tn, fp, fn, tp = cm.ravel()

# Metrics
accuracy = (tp + tn) / (tp + tn + fp + fn)
precision = tp / (tp + fp) if (tp + fp) > 0 else 0
recall = tp / (tp + fn) if (tp + fn) > 0 else 0
f1 = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0

print("\nClassification Results:")
print("=" * 50)
print(f"Accuracy:         {accuracy:.3f}")
print(f"Precision:        {precision:.3f}")
print(f"Recall:           {recall:.3f}")
print(f"F1-Score:         {f1:.3f}")

print("\nConfusion Matrix:")
print(f"True Negatives:   {tn}")
print(f"False Positives:  {fp}")
print(f"False Negatives:  {fn}")
print(f"True Positives:   {tp}")


# Analyze by confidence level
print("\nPerformance by confidence level:")
for conf in ['high', 'medium', 'low', 'none']:
    subset = feature_df[feature_df['confidence_category'] == conf]
    if len(subset) > 0:
        acc = (subset['true_pile'] == subset['predicted_pile']).mean()
        print(f"  {conf.capitalize()}: {len(subset)} patches, Accuracy: {acc:.3f}")

# Analyze by data split
print("\nPerformance by data split:")
for split in ['train', 'val', 'test']:
    subset = feature_df[feature_df['split'] == split]
    if len(subset) > 0:
        acc = (subset['true_pile'] == subset['predicted_pile']).mean()
        print(f"  {split.capitalize()}: {len(subset)} patches, Accuracy: {acc:.3f}")

print("Saving enhanced analysis results...")

# Save main feature dataframe
feature_df.to_csv(output_dir / "feature_analysis_results.csv", index=False)

# Save per-split CSVs
for split in ['train', 'val', 'test']:
    split_df = feature_df[feature_df['split'] == split]
    if not split_df.empty:
        split_df.to_csv(output_dir / f"feature_analysis_{split}.csv", index=False)

# Save comparison stats
comparison_df.to_csv(output_dir / "feature_comparison.csv", index=False)
