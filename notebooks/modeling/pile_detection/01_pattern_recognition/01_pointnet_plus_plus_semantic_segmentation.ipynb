# Core libraries
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader

# Data processing and visualization
import numpy as np
import open3d as o3d
import matplotlib.pyplot as plt
import seaborn as sns

# File and path utilities
from pathlib import Path
import json
import pickle
from datetime import datetime
import warnings

# Suppress warnings
warnings.filterwarnings('ignore')

# Print versions for compatibility checks
print(f"NumPy version: {np.__version__}")
print(f"PyTorch version: {torch.__version__}")

# Select device
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"Using device: {device}")


def square_distance(src, dst):
    """
    Calculate Euclidean distance between each two points.
    Input:
        src: source points, [B, N, C]
        dst: target points, [B, M, C]
    Output:
        dist: per-point square distance, [B, N, M]
    """
    B, N, _ = src.shape
    _, M, _ = dst.shape
    dist = -2 * torch.matmul(src, dst.permute(0, 2, 1))     # -2xy^T
    dist += torch.sum(src ** 2, -1).view(B, N, 1)            # +x^2
    dist += torch.sum(dst ** 2, -1).view(B, 1, M)            # +y^2
    return dist

def index_points(points, idx):
    """
    Index points along the second dimension.
    Input:
        points: input points data, [B, N, C]
        idx: sample index data, [B, S] or [B, S, K]
    Return:
        new_points: indexed points data, [B, S, C] or [B, S, K, C]
    """
    device = points.device
    B = points.shape[0]
    view_shape = list(idx.shape)
    view_shape[1:] = [1] * (len(view_shape) - 1)   # e.g., [B, 1, 1]
    repeat_shape = list(idx.shape)
    repeat_shape[0] = 1
    batch_indices = torch.arange(B, dtype=torch.long).to(device).view(view_shape).repeat(repeat_shape)
    new_points = points[batch_indices, idx, :]
    return new_points


def farthest_point_sample(xyz, npoint):
    """Farthest Point Sampling"""
    device = xyz.device
    B, N, C = xyz.shape
    centroids = torch.zeros(B, npoint, dtype=torch.long).to(device)
    distance = torch.ones(B, N).to(device) * 1e10
    farthest = torch.randint(0, N, (B,), dtype=torch.long).to(device)
    batch_indices = torch.arange(B, dtype=torch.long).to(device)
    
    for i in range(npoint):
        centroids[:, i] = farthest
        centroid = xyz[batch_indices, farthest, :].view(B, 1, 3)
        dist = torch.sum((xyz - centroid) ** 2, -1)
        mask = dist < distance
        distance[mask] = dist[mask]
        farthest = torch.max(distance, -1)[1]
    
    return centroids

def query_ball_point(radius, nsample, xyz, new_xyz):
    """Ball query"""
    device = xyz.device
    B, N, C = xyz.shape
    _, S, _ = new_xyz.shape
    group_idx = torch.arange(N, dtype=torch.long).to(device).view(1, 1, N).repeat([B, S, 1])
    sqrdists = square_distance(new_xyz, xyz)
    group_idx[sqrdists > radius ** 2] = N
    group_idx = group_idx.sort(dim=-1)[0][:, :, :nsample]
    group_first = group_idx[:, :, 0].view(B, S, 1).repeat([1, 1, nsample])
    mask = group_idx == N
    group_idx[mask] = group_first[mask]
    return group_idx

def sample_and_group(npoint, radius, nsample, xyz, points, returnfps=False):
    """Sample and group points for hierarchical processing"""
    B, N, C = xyz.shape
    S = npoint
    
    # Sample points using farthest point sampling
    fps_idx = farthest_point_sample(xyz, npoint)  # [B, npoint]
    new_xyz = index_points(xyz, fps_idx)
    
    # Group neighboring points
    idx = query_ball_point(radius, nsample, xyz, new_xyz)
    grouped_xyz = index_points(xyz, idx)  # [B, npoint, nsample, C]
    grouped_xyz_norm = grouped_xyz - new_xyz.view(B, S, 1, C)
    
    if points is not None:
        grouped_points = index_points(points, idx)
        new_points = torch.cat([grouped_xyz_norm, grouped_points], dim=-1)  # [B, npoint, nsample, C+D]
    else:
        new_points = grouped_xyz_norm
    
    if returnfps:
        return new_xyz, new_points, grouped_xyz, fps_idx
    else:
        return new_xyz, new_points

def sample_and_group_all(xyz, points):
    """Group all points (used in final layer)"""
    device = xyz.device
    B, N, C = xyz.shape
    new_xyz = torch.zeros(B, 1, C).to(device)
    grouped_xyz = xyz.view(B, 1, N, C)
    if points is not None:
        new_points = torch.cat([grouped_xyz, points.view(B, 1, N, -1)], dim=-1)
    else:
        new_points = grouped_xyz
    return new_xyz, new_points

class PointNetSetAbstraction(nn.Module):
    """PointNet Set Abstraction Layer"""
    def __init__(self, npoint, radius, nsample, in_channel, mlp, group_all):
        super(PointNetSetAbstraction, self).__init__()
        self.npoint = npoint
        self.radius = radius
        self.nsample = nsample
        self.mlp_convs = nn.ModuleList()
        self.mlp_bns = nn.ModuleList()
        self.group_all = group_all

        last_channel = in_channel

        # Build MLP layers
        for out_channel in mlp:
            self.mlp_convs.append(nn.Conv2d(last_channel, out_channel, 1))
            self.mlp_bns.append(nn.BatchNorm2d(out_channel))
            last_channel = out_channel

    def forward(self, xyz, points):
        xyz = xyz.permute(0, 2, 1)
        if points is not None:
            points = points.permute(0, 2, 1)

        # if self.group_all:
        #     new_xyz, new_points = sample_and_group_all(xyz, points)
        # else:
        #     new_xyz, new_points = sample_and_group(self.npoint, self.radius, self.nsample, xyz, points)

        if self.group_all:
            new_xyz = torch.zeros(xyz.shape[0], 1, xyz.shape[2]).to(xyz.device)
            new_points = xyz.view(xyz.shape[0], 1, xyz.shape[1], xyz.shape[2])
            if points is not None:
                new_points = torch.cat([new_points, points.view(points.shape[0], 1, points.shape[1], points.shape[2])], dim=-1)
        else:
            fps_idx = farthest_point_sample(xyz, self.npoint)
            new_xyz = index_points(xyz, fps_idx)
            idx = query_ball_point(self.radius, self.nsample, xyz, new_xyz)
            grouped_xyz = index_points(xyz, idx)
            grouped_xyz_norm = grouped_xyz - new_xyz.view(xyz.shape[0], self.npoint, 1, xyz.shape[2])
            
            if points is not None:
                grouped_points = index_points(points, idx)
                new_points = torch.cat([grouped_xyz_norm, grouped_points], dim=-1)
            else:
                new_points = grouped_xyz_norm

        # Apply MLP to grouped points
        new_points = new_points.permute(0, 3, 2, 1)  # [B, C+D, nsample, npoint]
        for i, conv in enumerate(self.mlp_convs):
            bn = self.mlp_bns[i]
            new_points = F.relu(bn(conv(new_points)))

        # Max pooling to get point-wise features
        new_points = torch.max(new_points, 2)[0]
        new_xyz = new_xyz.permute(0, 2, 1)
        return new_xyz, new_points

class PointNetFeaturePropagation(nn.Module):
    """Feature Propagation Layer for PointNet++"""
    def __init__(self, in_channel, mlp):
        super(PointNetFeaturePropagation, self).__init__()
        self.mlp_convs = nn.ModuleList()
        self.mlp_bns = nn.ModuleList()
        last_channel = in_channel
        
        for out_channel in mlp:
            self.mlp_convs.append(nn.Conv1d(last_channel, out_channel, 1))
            self.mlp_bns.append(nn.BatchNorm1d(out_channel))
            last_channel = out_channel

    def forward(self, xyz1, xyz2, points1, points2):
        xyz1 = xyz1.permute(0, 2, 1)
        xyz2 = xyz2.permute(0, 2, 1)
        points2 = points2.permute(0, 2, 1)
        B, N, C = xyz1.shape
        _, S, _ = xyz2.shape

        if S == 1:
            interpolated_points = points2.repeat(1, N, 1)
        else:
            # Interpolate using inverse distance weighting
            dists = square_distance(xyz1, xyz2)
            dists, idx = dists.sort(dim=-1)
            dists, idx = dists[:, :, :3], idx[:, :, :3]  # [B, N, 3]

            dist_recip = 1.0 / (dists + 1e-8)
            norm = torch.sum(dist_recip, dim=2, keepdim=True)
            weight = dist_recip / norm
            interpolated_points = torch.sum(index_points(points2, idx) * weight.view(B, N, 3, 1), dim=2)

        if points1 is not None:
            points1 = points1.permute(0, 2, 1)
            new_points = torch.cat([points1, interpolated_points], dim=-1)
        else:
            new_points = interpolated_points

        new_points = new_points.permute(0, 2, 1)
        for i, conv in enumerate(self.mlp_convs):
            bn = self.mlp_bns[i]
            new_points = F.relu(bn(conv(new_points)))
        return new_points

# Not needed - as we used DBSCAN for clustering
class PileBufferSegmentation(nn.Module):
    """
    PointNet++ optimized for pile buffer zone segmentation
    Designed for distinguishing piles from clutter in buffered regions
    """
    def __init__(self, num_classes=6):  # pile, vegetation, jcb, mud, pallet, other
        super(PileBufferSegmentation, self).__init__()
        self.num_classes = num_classes
        
        # Enhanced feature extraction for construction objects
        # Smaller radii for detailed local features important for construction objects
        self.sa1 = PointNetSetAbstraction(1024, 0.1, 32, 3, [32, 32, 64], False)
        self.sa2 = PointNetSetAbstraction(256, 0.2, 32, 64 + 3, [64, 64, 128], False)
        self.sa3 = PointNetSetAbstraction(64, 0.4, 32, 128 + 3, [128, 128, 256], False)
        self.sa4 = PointNetSetAbstraction(16, 0.8, 32, 256 + 3, [256, 256, 512], False)
        self.sa5 = PointNetSetAbstraction(None, None, None, 512 + 3, [512, 512, 1024], True)
        
        # Feature propagation with enhanced channels for detailed reconstruction
        self.fp5 = PointNetFeaturePropagation(1536, [512, 512])
        self.fp4 = PointNetFeaturePropagation(768, [512, 256])
        self.fp3 = PointNetFeaturePropagation(384, [256, 256])
        self.fp2 = PointNetFeaturePropagation(320, [256, 128])
        self.fp1 = PointNetFeaturePropagation(128, [128, 128, 128])
        
        # Enhanced classification head with attention mechanism
        self.conv1 = nn.Conv1d(128, 128, 1)
        self.bn1 = nn.BatchNorm1d(128)
        self.drop1 = nn.Dropout(0.3)
        
        # Attention mechanism for important feature selection
        self.attention = nn.Conv1d(128, 128, 1)
        self.attention_bn = nn.BatchNorm1d(128)
        
        self.conv2 = nn.Conv1d(128, 64, 1)
        self.bn2 = nn.BatchNorm1d(64)
        self.drop2 = nn.Dropout(0.3)
        
        self.conv3 = nn.Conv1d(64, num_classes, 1)

    def forward(self, xyz):
        B, _, N = xyz.shape
        
        # Hierarchical feature extraction with more levels for detailed analysis
        l1_xyz, l1_points = self.sa1(xyz, None)      # 2048 -> 1024
        l2_xyz, l2_points = self.sa2(l1_xyz, l1_points)  # 1024 -> 256  
        l3_xyz, l3_points = self.sa3(l2_xyz, l2_points)  # 256 -> 64
        l4_xyz, l4_points = self.sa4(l3_xyz, l3_points)  # 64 -> 16
        l5_xyz, l5_points = self.sa5(l4_xyz, l4_points)  # 16 -> 1
        
        # Hierarchical feature propagation
        l4_points = self.fp5(l4_xyz, l5_xyz, l4_points, l5_points)
        l3_points = self.fp4(l3_xyz, l4_xyz, l3_points, l4_points)
        l2_points = self.fp3(l2_xyz, l3_xyz, l2_points, l3_points)
        l1_points = self.fp2(l1_xyz, l2_xyz, l1_points, l2_points)
        l0_points = self.fp1(xyz, l1_xyz, None, l1_points)
        
        # Enhanced classification with attention
        feat = self.drop1(F.relu(self.bn1(self.conv1(l0_points))))
        
        # Apply attention mechanism
        attention_weights = torch.sigmoid(self.attention_bn(self.attention(feat)))
        feat = feat * attention_weights
        
        feat = self.drop2(F.relu(self.bn2(self.conv2(feat))))
        feat = self.conv3(feat)
        
        return feat.permute(0, 2, 1)  # [B, N, num_classes]


def sample_and_group(npoint, radius, nsample, xyz, points, returnfps=False):
    """Sample and group points for hierarchical processing"""
    B, N, C = xyz.shape
    S = npoint
    
    # Sample points using farthest point sampling
    fps_idx = farthest_point_sample(xyz, npoint)  # [B, npoint]
    new_xyz = index_points(xyz, fps_idx)
    
    # Group neighboring points
    idx = query_ball_point(radius, nsample, xyz, new_xyz)
    grouped_xyz = index_points(xyz, idx)  # [B, npoint, nsample, C]
    grouped_xyz_norm = grouped_xyz - new_xyz.view(B, S, 1, C)
    
    if points is not None:
        grouped_points = index_points(points, idx)
        new_points = torch.cat([grouped_xyz_norm, grouped_points], dim=-1)  # [B, npoint, nsample, C+D]
    else:
        new_points = grouped_xyz_norm
    
    if returnfps:
        return new_xyz, new_points, grouped_xyz, fps_idx
    else:
        return new_xyz, new_points

def sample_and_group_all(xyz, points):
    """Group all points (used in final layer)"""
    device = xyz.device
    B, N, C = xyz.shape
    new_xyz = torch.zeros(B, 1, C).to(device)
    grouped_xyz = xyz.view(B, 1, N, C)
    if points is not None:
        new_points = torch.cat([grouped_xyz, points.view(B, 1, N, -1)], dim=-1)
    else:
        new_points = grouped_xyz
    return new_xyz, new_points

class PointNetSetAbstraction(nn.Module):
    """PointNet Set Abstraction Layer"""
    def __init__(self, npoint, radius, nsample, in_channel, mlp, group_all):
        super(PointNetSetAbstraction, self).__init__()
        self.npoint = npoint
        self.radius = radius
        self.nsample = nsample
        self.mlp_convs = nn.ModuleList()
        self.mlp_bns = nn.ModuleList()
        last_channel = in_channel
        
        # Build MLP layers
        for out_channel in mlp:
            self.mlp_convs.append(nn.Conv2d(last_channel, out_channel, 1))
            self.mlp_bns.append(nn.BatchNorm2d(out_channel))
            last_channel = out_channel
        self.group_all = group_all

    def forward(self, xyz, points):
        """
        Input:
            xyz: input points position data, [B, C, N]
            points: input points data, [B, D, N]
        Return:
            new_xyz: sampled points position data, [B, C, S]
            new_points_concat: sample points feature data, [B, D', S]
        """
        xyz = xyz.permute(0, 2, 1)
        if points is not None:
            points = points.permute(0, 2, 1)

        if self.group_all:
            new_xyz, new_points = sample_and_group_all(xyz, points)
        else:
            new_xyz, new_points = sample_and_group(self.npoint, self.radius, self.nsample, xyz, points)
        
        # Apply MLP to grouped points
        new_points = new_points.permute(0, 3, 2, 1)  # [B, C+D, nsample, npoint]
        for i, conv in enumerate(self.mlp_convs):
            bn = self.mlp_bns[i]
            new_points = F.relu(bn(conv(new_points)))

        # Max pooling to get point-wise features
        new_points = torch.max(new_points, 2)[0]
        new_xyz = new_xyz.permute(0, 2, 1)
        return new_xyz, new_points

class PointNetFeaturePropagation(nn.Module):
    """Feature Propagation Layer for PointNet++"""
    def __init__(self, in_channel, mlp):
        super(PointNetFeaturePropagation, self).__init__()
        self.mlp_convs = nn.ModuleList()
        self.mlp_bns = nn.ModuleList()
        last_channel = in_channel
        
        for out_channel in mlp:
            self.mlp_convs.append(nn.Conv1d(last_channel, out_channel, 1))
            self.mlp_bns.append(nn.BatchNorm1d(out_channel))
            last_channel = out_channel

    def forward(self, xyz1, xyz2, points1, points2):
        """
        Input:
            xyz1: input points position data, [B, C, N]
            xyz2: sampled input points position data, [B, C, S]
            points1: input points data, [B, D, N]
            points2: input points data, [B, D, S]
        Return:
            new_points: upsampled points data, [B, D', N]
        """
        xyz1 = xyz1.permute(0, 2, 1)
        xyz2 = xyz2.permute(0, 2, 1)
        points2 = points2.permute(0, 2, 1)
        B, N, C = xyz1.shape
        _, S, _ = xyz2.shape

        if S == 1:
            interpolated_points = points2.repeat(1, N, 1)
        else:
            # Interpolate using inverse distance weighting
            dists = square_distance(xyz1, xyz2)
            dists, idx = dists.sort(dim=-1)
            dists, idx = dists[:, :, :3], idx[:, :, :3]  # [B, N, 3]

            dist_recip = 1.0 / (dists + 1e-8)
            norm = torch.sum(dist_recip, dim=2, keepdim=True)
            weight = dist_recip / norm
            interpolated_points = torch.sum(index_points(points2, idx) * weight.view(B, N, 3, 1), dim=2)

        if points1 is not None:
            points1 = points1.permute(0, 2, 1)
            new_points = torch.cat([points1, interpolated_points], dim=-1)
        else:
            new_points = interpolated_points

        new_points = new_points.permute(0, 2, 1)
        for i, conv in enumerate(self.mlp_convs):
            bn = self.mlp_bns[i]
            new_points = F.relu(bn(conv(new_points)))
        return new_points

class PointNetPlusPlusSegmentation(nn.Module):
    """PointNet++ for Semantic Segmentation"""
    def __init__(self, num_classes=4):
        super(PointNetPlusPlusSegmentation, self).__init__()
        self.num_classes = num_classes
        
        # Set Abstraction layers (encoder)
        self.sa1 = PointNetSetAbstraction(npoint=512, radius=0.2, nsample=32, in_channel=3, mlp=[64, 64, 128], group_all=False)
        self.sa2 = PointNetSetAbstraction(npoint=128, radius=0.4, nsample=64, in_channel=128 + 3, mlp=[128, 128, 256], group_all=False)
        self.sa3 = PointNetSetAbstraction(npoint=None, radius=None, nsample=None, in_channel=256 + 3, mlp=[256, 512, 1024], group_all=True)
        
        # Feature Propagation layers (decoder)
        self.fp3 = PointNetFeaturePropagation(in_channel=1280, mlp=[256, 256])
        self.fp2 = PointNetFeaturePropagation(in_channel=384, mlp=[256, 128])
        self.fp1 = PointNetFeaturePropagation(in_channel=128, mlp=[128, 128, 128])
        
        # Classification head
        self.conv1 = nn.Conv1d(128, 128, 1)
        self.bn1 = nn.BatchNorm1d(128)
        self.drop1 = nn.Dropout(0.5)
        self.conv2 = nn.Conv1d(128, num_classes, 1)

    def forward(self, xyz):
        B, _, N = xyz.shape
        
        # Set Abstraction layers (downsampling)
        l1_xyz, l1_points = self.sa1(xyz, None)
        l2_xyz, l2_points = self.sa2(l1_xyz, l1_points)
        l3_xyz, l3_points = self.sa3(l2_xyz, l2_points)
        
        # Feature Propagation layers (upsampling)
        l2_points = self.fp3(l2_xyz, l3_xyz, l2_points, l3_points)
        l1_points = self.fp2(l1_xyz, l2_xyz, l1_points, l2_points)
        l0_points = self.fp1(xyz, l1_xyz, None, l1_points)
        
        # Classification
        feat = F.relu(self.bn1(self.conv1(l0_points)))
        feat = self.drop1(feat)
        feat = self.conv2(feat)
        feat = F.log_softmax(feat, dim=1)
        
        return feat.permute(0, 2, 1)

class PileClusterDataset(Dataset):
    """Enhanced dataset for PointNet++ with better preprocessing"""
    
    def __init__(self, cluster_dir, labels_dir=None, num_points=4096, augment=False):
        self.cluster_dir = Path(cluster_dir)
        self.labels_dir = Path(labels_dir) if labels_dir else None
        self.num_points = num_points
        self.augment = augment
        
        # Class mapping for construction elements
        self.class_map = {
            'pile': 0,
            'support': 1, 
            'pallet': 2,
            'machinery': 3,
            'non-pile': 4
        }

        self.num_classes = len(self.class_map)
        
        # Load cluster files
        self.cluster_files = list(self.cluster_dir.glob("*.ply"))
        print(f"Found {len(self.cluster_files)} cluster files")
        
    def __len__(self):
        return len(self.cluster_files)
    
    def __getitem__(self, idx):
        cluster_file = self.cluster_files[idx]
        
        # Load point cloud
        pcd = o3d.io.read_point_cloud(str(cluster_file))
        points = np.asarray(pcd.points)
        
        # Enhanced preprocessing for PointNet++
        points = self.process_points_enhanced(points)
        
        # Load labels if available
        if self.labels_dir:
            label_file = self.labels_dir / f"{cluster_file.stem}_labels.npy"
            if label_file.exists():
                labels = np.load(label_file)
                labels = self.process_labels(labels, len(points))
            else:
                labels = np.full(len(points), self.class_map['non-pile'], dtype=np.int64)  # default
        else:
            labels = np.full(len(points), -1, dtype=np.int64)  # unlabeled
            
        # Data augmentation
        if self.augment:
            points = self.augment_points(points)
            
        return {
            'points': torch.FloatTensor(points).transpose(1, 0),  # [3, N]
            'labels': torch.LongTensor(labels),
            'cluster_id': cluster_file.stem
        }
    
    def process_points_enhanced(self, points):
        """Enhanced point processing for PointNet++"""
        # Remove outliers first
        if len(points) > 100:
            pcd = o3d.geometry.PointCloud()
            pcd.points = o3d.utility.Vector3dVector(points)
            pcd, _ = pcd.remove_statistical_outlier(nb_neighbors=20, std_ratio=2.0)
            points = np.asarray(pcd.points)
        
        # Sample or pad points
        if len(points) >= self.num_points:
            # Use farthest point sampling for better coverage
            indices = self.farthest_point_sampling_numpy(points, self.num_points)
            points = points[indices]
        else:
            # Pad with jittered copies
            n_repeats = (self.num_points // len(points)) + 1
            repeated_points = np.tile(points, (n_repeats, 1))
            
            # Add small jitter to avoid identical points
            jitter = np.random.normal(0, 0.01, repeated_points.shape)
            repeated_points = repeated_points + jitter
            
            points = repeated_points[:self.num_points]
            
        # Normalize to unit sphere (better for PointNet++)
        centroid = np.mean(points, axis=0)
        points = points - centroid
        max_dist = np.max(np.linalg.norm(points, axis=1))
        if max_dist > 0:
            points = points / max_dist
            
        return points
    
    def farthest_point_sampling_numpy(self, points, num_samples):
        """Numpy implementation of farthest point sampling"""
        n_points = len(points)
        selected = np.zeros(num_samples, dtype=np.int64)
        distances = np.full(n_points, np.inf)
        
        # Start with random point
        selected[0] = np.random.randint(0, n_points)
        
        for i in range(1, num_samples):
            last_selected = selected[i-1]
            # Calculate distances to last selected point
            dists = np.linalg.norm(points - points[last_selected], axis=1)
            # Update minimum distances
            distances = np.minimum(distances, dists)
            # Select farthest point
            selected[i] = np.argmax(distances)
            
        return selected
    
    def process_labels(self, labels, target_length):
        """Process labels to match point count"""
        if len(labels) >= target_length:
            # Use same indices as point sampling
            indices = self.farthest_point_sampling_numpy(
                np.arange(len(labels)).reshape(-1, 1), target_length
            )
            return labels[indices]
        else:
            repeats = target_length // len(labels) + 1
            labels = np.tile(labels, repeats)[:target_length]
            return labels
    
    def augment_points(self, points):
        """Enhanced data augmentation for construction scenes"""
        # Random rotation around Z-axis (vertical structures)
        angle = np.random.uniform(0, 2 * np.pi)
        cos_a, sin_a = np.cos(angle), np.sin(angle)
        rotation = np.array([[cos_a, -sin_a, 0],
                           [sin_a, cos_a, 0],
                           [0, 0, 1]])
        points = points @ rotation.T
        
        # Random scaling (realistic for construction tolerance)
        scale = np.random.uniform(0.95, 1.05)
        points = points * scale
        
        # Random jittering (sensor noise simulation)
        noise = np.random.normal(0, 0.01, points.shape)
        points = points + noise
        
        return points

def train_epoch(model, dataloader, optimizer, criterion, device):
    """Train for one epoch with PointNet++"""
    model.train()
    running_loss = 0.0
    correct = 0
    total = 0
    
    for batch_idx, data in enumerate(dataloader):
        points = data['points'].to(device)  # [B, 3, N]
        labels = data['labels'].to(device)  # [B, N]
        
        optimizer.zero_grad()
        
        pred = model(points)  # [B, N, num_classes]
        #pred = pred.view(-1, model.num_classes)
        pred = pred.reshape(-1, model.num_classes)
        labels = labels.view(-1)
        
        # Filter out invalid labels
        valid_mask = labels >= 0
        if valid_mask.sum() == 0:
            continue
            
        pred = pred[valid_mask]
        labels = labels[valid_mask]
        
        loss = criterion(pred, labels)
        loss.backward()
        optimizer.step()
        
        running_loss += loss.item()
        _, predicted = torch.max(pred, 1)
        total += labels.size(0)
        correct += (predicted == labels).sum().item()
        
        if batch_idx % 10 == 0:
            print(f'Batch {batch_idx}/{len(dataloader)}, Loss: {loss.item():.4f}')
    
    accuracy = 100. * correct / total if total > 0 else 0
    avg_loss = running_loss / len(dataloader)
    
    return avg_loss, accuracy

def validate_epoch(model, dataloader, criterion, device):
    """Validate for one epoch"""
    model.eval()
    running_loss = 0.0
    correct = 0
    total = 0
    all_preds = []
    all_labels = []
    
    with torch.no_grad():
        for data in dataloader:
            points = data['points'].to(device)
            labels = data['labels'].to(device)
            
            pred = model(points)
            #pred = pred.view(-1, model.num_classes)
            pred = pred.reshape(-1, model.num_classes)
            labels = labels.view(-1)
            
            # Filter out invalid labels
            valid_mask = labels >= 0
            if valid_mask.sum() == 0:
                continue
                
            pred = pred[valid_mask]
            labels = labels[valid_mask]
            
            loss = criterion(pred, labels)
            
            running_loss += loss.item()
            _, predicted = torch.max(pred, 1)
            total += labels.size(0)
            correct += (predicted == labels).sum().item()
            
            all_preds.extend(predicted.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
    
    accuracy = 100. * correct / total if total > 0 else 0
    avg_loss = running_loss / len(dataloader)
    
    return avg_loss, accuracy, all_preds, all_labels

def train_pointnet_plus_segmentation():
    """Main training function for PointNet++"""
    
    # Parameters
    CLUSTER_DIR = "labeled_clusters"
    LABELS_DIR = "labeled_clusters"
    NUM_POINTS = 4096  # Increased for better performance
    BATCH_SIZE = 4     # Reduced due to memory requirements
    NUM_EPOCHS = 50    # Reduced for demonstration
    LEARNING_RATE = 0.001
    
    print(f"Training Parameters:")
    print(f"- Number of points per cluster: {NUM_POINTS}")
    print(f"- Batch size: {BATCH_SIZE}")
    print(f"- Number of epochs: {NUM_EPOCHS}")
    print(f"- Learning rate: {LEARNING_RATE}")
    
    # Create dataset
    dataset = PileClusterDataset(
        cluster_dir=CLUSTER_DIR,
        labels_dir=LABELS_DIR,
        num_points=NUM_POINTS,
        augment=True
    )
    
    if len(dataset) == 0:
        print("No data found. Please ensure cluster files exist in the specified directory.")
        return None, None
    
    # Split dataset
    train_size = int(0.8 * len(dataset))
    val_size = len(dataset) - train_size
    train_dataset, val_dataset = torch.utils.data.random_split(dataset, [train_size, val_size])
    
    print(f"Dataset split: {train_size} training, {val_size} validation")
    
    # Create dataloaders
    train_loader = DataLoader(train_dataset, batch_size=4, shuffle=True, num_workers=0)
    val_loader = DataLoader(val_dataset, batch_size=4, shuffle=False, num_workers=0)
    
    # Initialize model
    model = PointNetPlusPlusSegmentation(num_classes=dataset.num_classes).to(device)
    print(f"Model initialized with {dataset.num_classes} classes")
    
    # Loss and optimizer with class weighting
    class_weights = torch.tensor([1.0, 1.2, 1.1, 0.8]).to(device)  # Emphasize pile/support classes
    criterion = nn.NLLLoss(weight=class_weights)
    optimizer = optim.Adam(model.parameters(), lr=LEARNING_RATE, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=NUM_EPOCHS)
    
    # Training loop
    train_losses = []
    train_accuracies = []
    val_losses = []
    val_accuracies = []
    
    best_val_acc = 0.0
    
    print("\nStarting training...")
    for epoch in range(NUM_EPOCHS):
        print(f'\nEpoch {epoch+1}/{NUM_EPOCHS}')
        print('-' * 50)
        
        # Train
        train_loss, train_acc = train_epoch(model, train_loader, optimizer, criterion, device)
        train_losses.append(train_loss)
        train_accuracies.append(train_acc)
        
        # Validate
        val_loss, val_acc, val_preds, val_labels = validate_epoch(model, val_loader, criterion, device)
        val_losses.append(val_loss)
        val_accuracies.append(val_acc)
        
        scheduler.step()
        
        print(f'Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.2f}%')
        print(f'Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.2f}%')
        print(f'Learning Rate: {scheduler.get_last_lr()[0]:.6f}')
        
        # Save best model
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_acc': val_acc,
                'class_map': dataset.class_map,
                'num_points': NUM_POINTS
            }, 'best_pointnet_plus_pile_segmentation.pth')
            print(f'New best model saved with val acc: {val_acc:.2f}%')
    
    return model, dataset.class_map

class PointwiseSegmentationDataset(Dataset):
    """Dataset for pointwise segmentation with enhanced preprocessing"""
    
    def __init__(self, cluster_dir, labels_dir=None, num_points=4096, augment=False):
        self.cluster_dir = Path(cluster_dir)
        self.labels_dir = Path(labels_dir) if labels_dir else None
        self.num_points = num_points
        self.augment = augment
        
        # Class mapping for construction elements
        self.class_map = {
            'pile': 0,
            'support': 1, 
            'pallet': 2,
            'non-pile': 3
        }
        self.num_classes = len(self.class_map)
        
        # Load cluster files
        self.cluster_files = list(self.cluster_dir.glob("*.ply"))
        print(f"Found {len(self.cluster_files)} cluster files")
        
    def __len__(self):
        return len(self.cluster_files)
    
    def __getitem__(self, idx):
        cluster_file = self.cluster_files[idx]
        
        # Load point cloud
        pcd = o3d.io.read_point_cloud(str(cluster_file))
        points = np.asarray(pcd.points)
        
        # Enhanced preprocessing
        points = self.process_points_enhanced(points)
        
        # Load labels if available
        if self.labels_dir:
            label_file = self.labels_dir / f"{cluster_file.stem}_labels.npy"
            if label_file.exists():
                labels = np.load(label_file)
                labels = self.process_labels(labels, len(points))
            else:
                labels = np.full(len(points), self.class_map['non-pile'], dtype=np.int64)
        else:
            labels = np.full(len(points), -1, dtype=np.int64)
            
        # Data augmentation
        if self.augment:
            points = self.augment_points(points)
            
        return {
            'points': torch.FloatTensor(points).transpose(1, 0),  # [3, N]
            'labels': torch.LongTensor(labels),
            'cluster_id': cluster_file.stem
        }
    
    def process_points_enhanced(self, points):
        """Enhanced point processing for PointNet++"""
        # Remove outliers first
        if len(points) > 100:
            pcd = o3d.geometry.PointCloud()
            pcd.points = o3d.utility.Vector3dVector(points)
            pcd, _ = pcd.remove_statistical_outlier(nb_neighbors=20, std_ratio=2.0)
            points = np.asarray(pcd.points)
        
        # Sample or pad points
        if len(points) >= self.num_points:
            # Use farthest point sampling for better coverage
            indices = self.farthest_point_sampling_numpy(points, self.num_points)
            points = points[indices]
        else:
            # Pad with jittered copies
            n_repeats = (self.num_points // len(points)) + 1
            repeated_points = np.tile(points, (n_repeats, 1))
            
            # Add small jitter to avoid identical points
            jitter = np.random.normal(0, 0.01, repeated_points.shape)
            repeated_points = repeated_points + jitter
            
            points = repeated_points[:self.num_points]
            
        # Normalize to unit sphere (better for PointNet++)
        centroid = np.mean(points, axis=0)
        points = points - centroid
        max_dist = np.max(np.linalg.norm(points, axis=1))
        if max_dist > 0:
            points = points / max_dist
            
        return points
    
    def farthest_point_sampling_numpy(self, points, num_samples):
        """Numpy implementation of farthest point sampling"""
        n_points = len(points)
        selected = np.zeros(num_samples, dtype=np.int64)
        distances = np.full(n_points, np.inf)
        
        # Start with random point
        selected[0] = np.random.randint(0, n_points)
        
        for i in range(1, num_samples):
            last_selected = selected[i-1]
            # Calculate distances to last selected point
            dists = np.linalg.norm(points - points[last_selected], axis=1)
            # Update minimum distances
            distances = np.minimum(distances, dists)
            # Select farthest point
            selected[i] = np.argmax(distances)
            
        return selected
    
    def process_labels(self, labels, target_length):
        """Process labels to match point count"""
        if len(labels) >= target_length:
            # Use same indices as point sampling
            indices = self.farthest_point_sampling_numpy(
                np.arange(len(labels)).reshape(-1, 1), target_length
            )
            return labels[indices]
        else:
            repeats = target_length // len(labels) + 1
            labels = np.tile(labels, repeats)[:target_length]
            return labels
    
    def augment_points(self, points):
        """Enhanced data augmentation for construction scenes"""
        # Random rotation around Z-axis (vertical structures)
        angle = np.random.uniform(0, 2 * np.pi)
        cos_a, sin_a = np.cos(angle), np.sin(angle)
        rotation = np.array([[cos_a, -sin_a, 0],
                           [sin_a, cos_a, 0],
                           [0, 0, 1]])
        points = points @ rotation.T
        
        # Random scaling (realistic for construction tolerance)
        scale = np.random.uniform(0.95, 1.05)
        points = points * scale
        
        # Random jittering (sensor noise simulation)
        noise = np.random.normal(0, 0.01, points.shape)
        points = points + noise
        
        return points

# Create dataset and examine class distribution
dataset = PointwiseSegmentationDataset(
    cluster_dir="labeled_clusters", 
    labels_dir="labeled_clusters", 
    patch_size=PATCH_SIZE, 
    pile_radius=PILE_RADIUS, 
    num_points=NUM_POINTS
)

# Check class balance (pile vs non-pile) in first few samples
pile_ratios = []

labels = np.zeros(dataset.num_points, dtype=np.int64)

for i in range(min(10, len(dataset))):
    _, labels = dataset[i]
    labels = torch.tensor(labels, dtype=torch.float32)  # Ensure tensor and float
    pile_ratio = (labels == 1).float().mean().item()
    pile_ratios.append(pile_ratio)

print("Pile Ratios (first 10 samples):", pile_ratios)

avg_pile_ratio = np.mean(pile_ratios)
print(f"Average pile point ratio across samples: {avg_pile_ratio:.3f}")
print(f"Class distribution: {1-avg_pile_ratio:.3f} non-pile, {avg_pile_ratio:.3f} pile")

# Split dataset into train/val/test
total_size = len(dataset)
train_size = int(TRAIN_SPLIT * total_size)
val_size = int(VAL_SPLIT * total_size)
test_size = total_size - train_size - val_size

train_dataset, val_dataset, test_dataset = torch.utils.data.random_split(
    dataset, [train_size, val_size, test_size], 
    generator=torch.Generator().manual_seed(42)
)

# Create data loaders
train_loader = DataLoader(train_dataset, batch_size=BATCH_SIZE, shuffle=True, num_workers=0)
val_loader = DataLoader(val_dataset, batch_size=BATCH_SIZE, shuffle=False, num_workers=0)
test_loader = DataLoader(test_dataset, batch_size=BATCH_SIZE, shuffle=False, num_workers=0)

print(f"Dataset splits:")
print(f"  Train: {len(train_dataset)} scenes")
print(f"  Validation: {len(val_dataset)} scenes")
print(f"  Test: {len(test_dataset)} scenes")

if MLFLOW_AVAILABLE:
    mlflow.log_metric("train_scenes", len(train_dataset))
    mlflow.log_metric("val_scenes", len(val_dataset))
    mlflow.log_metric("test_scenes", len(test_dataset))
    mlflow.log_metric("avg_pile_ratio", avg_pile_ratio)

def visualize_segmentation(points, labels, confidence_scores, class_map, title="PointNet++ Segmentation"):
    """Visualize segmentation results with confidence-based coloring"""
    # Create color map
    base_colors = {
        0: np.array([1, 0, 0]),      # pile - red
        1: np.array([0, 1, 0]),      # support - green  
        2: np.array([0, 0, 1]),      # pallet - blue
        3: np.array([0.5, 0.5, 0.5]) # non-pile - gray
    }
    
    point_colors = []
    for i, label in enumerate(labels):
        base_color = base_colors.get(label, np.array([0, 0, 0]))
        confidence = confidence_scores[i, label]
        # Modulate color intensity by confidence
        final_color = base_color * (0.3 + 0.7 * confidence)
        point_colors.append(final_color)
    
    point_colors = np.array(point_colors)
    
    # Create point cloud
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(points)
    pcd.colors = o3d.utility.Vector3dVector(point_colors)
    
    # Add coordinate frame for reference
    coord_frame = o3d.geometry.TriangleMesh.create_coordinate_frame(size=1.0)
    
    # Visualize
    o3d.visualization.draw_geometries([pcd, coord_frame], window_name=title)
    
    return pcd

def analyze_pile_structure(points, labels, confidence_scores, class_map):
    """Analyze the structure of segmented pile"""
    results = {}
    
    # Class distribution
    unique_labels, counts = np.unique(labels, return_counts=True)
    total_points = len(labels)
    
    class_distribution = {}
    for label, count in zip(unique_labels, counts):
        class_name = list(class_map.keys())[list(class_map.values()).index(label)]
        percentage = (count / total_points) * 100
        class_distribution[class_name] = {
            'count': int(count),
            'percentage': float(percentage)
        }
    
    # Extract pile points specifically
    pile_mask = labels == class_map['pile']
    if pile_mask.sum() > 0:
        pile_points = points[pile_mask]
        pile_confidence = confidence_scores[pile_mask, class_map['pile']]
        
        # Pile geometry analysis
        pile_centroid = np.mean(pile_points, axis=0)
        pile_height = np.max(pile_points[:, 2]) - np.min(pile_points[:, 2])
        
        # Estimate pile diameter (using 2D projection)
        pile_2d = pile_points[:, :2] - pile_centroid[:2]
        pile_radius_estimates = np.linalg.norm(pile_2d, axis=1)
        pile_diameter = 2 * np.percentile(pile_radius_estimates, 95)  # 95th percentile for robustness
        
        # Quality metrics
        avg_confidence = np.mean(pile_confidence)
        min_confidence = np.min(pile_confidence)
        
        results['pile_analysis'] = {
            'centroid': pile_centroid.tolist(),
            'height': float(pile_height),
            'estimated_diameter': float(pile_diameter),
            'avg_confidence': float(avg_confidence),
            'min_confidence': float(min_confidence),
            'quality_score': float(avg_confidence * (pile_mask.sum() / total_points))
        }
    else:
        results['pile_analysis'] = None
    
    results['class_distribution'] = class_distribution
    results['total_points'] = total_points
    
    return results

def calculate_iou_per_class(y_true, y_pred, num_classes, class_names):
    """Calculate Intersection over Union (IoU) for each class"""
    iou_scores = {}
    
    for class_idx in range(num_classes):
        class_name = class_names[class_idx]
        
        # True positives, false positives, false negatives
        tp = np.sum((y_true == class_idx) & (y_pred == class_idx))
        fp = np.sum((y_true != class_idx) & (y_pred == class_idx))
        fn = np.sum((y_true == class_idx) & (y_pred != class_idx))
        
        # Calculate IoU
        if tp + fp + fn == 0:
            iou = 0.0  # No instances of this class
        else:
            iou = tp / (tp + fp + fn)
        
        iou_scores[class_name] = {
            'iou': float(iou),
            'tp': int(tp),
            'fp': int(fp),
            'fn': int(fn)
        }
    
    # Calculate mean IoU
    mean_iou = np.mean([scores['iou'] for scores in iou_scores.values()])
    
    return iou_scores, mean_iou

def plot_confusion_matrix_and_iou(y_true, y_pred, class_names, title="Model Evaluation"):
    """Plot confusion matrix and display IoU metrics"""
    # Create figure with subplots
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # Plot confusion matrix
    cm = confusion_matrix(y_true, y_pred)
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                xticklabels=class_names, yticklabels=class_names, ax=ax1)
    ax1.set_title('Confusion Matrix')
    ax1.set_xlabel('Predicted')
    ax1.set_ylabel('Actual')
    
    # Calculate and display IoU scores
    iou_scores, mean_iou = calculate_iou_per_class(y_true, y_pred, len(class_names), class_names)
    
    # Plot IoU scores
    classes = list(iou_scores.keys())
    ious = [iou_scores[cls]['iou'] for cls in classes]
    
    bars = ax2.bar(classes, ious, color=['red', 'green', 'blue', 'gray'])
    ax2.set_title(f'Per-Class IoU (Mean IoU: {mean_iou:.3f})')
    ax2.set_ylabel('IoU Score')
    ax2.set_ylim(0, 1)
    
    # Add value labels on bars
    for bar, iou in zip(bars, ious):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                f'{iou:.3f}', ha='center', va='bottom')
    
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.suptitle(title, y=1.02)
    plt.show()
    
    # Print detailed IoU information
    print("\nDetailed IoU Analysis:")
    print("-" * 50)
    for class_name, scores in iou_scores.items():
        print(f"{class_name:>10}: IoU={scores['iou']:.3f} (TP={scores['tp']}, FP={scores['fp']}, FN={scores['fn']})")
    print(f"{'Mean IoU':>10}: {mean_iou:.3f}")
    
    return iou_scores, mean_iou

def visualize_predictions_detailed(points, true_labels, pred_labels, confidence_scores, class_map, 
                                 cluster_id="unknown", save_path=None):
    """Detailed visualization of predictions vs ground truth"""
    
    # Define colors for each class
    class_colors = {
        0: [1.0, 0.0, 0.0],    # pile - red
        1: [0.0, 1.0, 0.0],    # support - green
        2: [0.0, 0.0, 1.0],    # pallet - blue
        3: [0.5, 0.5, 0.5]     # non-pile - gray
    }
    
    # Create three point clouds: ground truth, predictions, and confidence-based
    fig = plt.figure(figsize=(18, 6))
    
    # Ground Truth Visualization
    pcd_gt = o3d.geometry.PointCloud()
    pcd_gt.points = o3d.utility.Vector3dVector(points)
    
    gt_colors = np.array([class_colors.get(label, [0, 0, 0]) for label in true_labels])
    pcd_gt.colors = o3d.utility.Vector3dVector(gt_colors)
    
    # Predictions Visualization
    pcd_pred = o3d.geometry.PointCloud()
    pcd_pred.points = o3d.utility.Vector3dVector(points)
    
    pred_colors = np.array([class_colors.get(label, [0, 0, 0]) for label in pred_labels])
    pcd_pred.colors = o3d.utility.Vector3dVector(pred_colors)
    
    # Confidence-based Visualization
    pcd_conf = o3d.geometry.PointCloud()
    pcd_conf.points = o3d.utility.Vector3dVector(points)
    
    conf_colors = []
    for i, (pred_label, conf_scores) in enumerate(zip(pred_labels, confidence_scores)):
        base_color = np.array(class_colors.get(pred_label, [0, 0, 0]))
        confidence = conf_scores[pred_label]
        # Modulate color intensity by confidence (low confidence = darker)
        final_color = base_color * (0.3 + 0.7 * confidence)
        conf_colors.append(final_color)
    
    pcd_conf.colors = o3d.utility.Vector3dVector(np.array(conf_colors))
    
    # Display all three visualizations
    print(f"\nVisualizing cluster: {cluster_id}")
    print("Close each window to proceed to the next visualization")
    
    # Ground truth
    print("\n1. Ground Truth Labels")
    o3d.visualization.draw_geometries([pcd_gt], 
                                    window_name=f"Ground Truth - {cluster_id}")
    
    # Predictions
    print("2. Model Predictions")
    o3d.visualization.draw_geometries([pcd_pred], 
                                    window_name=f"Predictions - {cluster_id}")
    
    # Confidence-based
    print("3. Confidence-based Coloring (darker = lower confidence)")
    o3d.visualization.draw_geometries([pcd_conf], 
                                    window_name=f"Confidence - {cluster_id}")
    
    # Save point clouds if path provided
    if save_path:
        save_path = Path(save_path)
        save_path.mkdir(exist_ok=True)
        
        o3d.io.write_point_cloud(str(save_path / f"{cluster_id}_ground_truth.ply"), pcd_gt)
        o3d.io.write_point_cloud(str(save_path / f"{cluster_id}_predictions.ply"), pcd_pred)
        o3d.io.write_point_cloud(str(save_path / f"{cluster_id}_confidence.ply"), pcd_conf)
        print(f"\nSaved visualizations to: {save_path}")
    
    return pcd_gt, pcd_pred, pcd_conf

def create_side_by_side_comparison(points, true_labels, pred_labels, confidence_scores, 
                                 class_map, cluster_id="comparison"):
    """Create a side-by-side comparison visualization"""
    
    class_colors = {
        0: [1.0, 0.0, 0.0],    # pile - red
        1: [0.0, 1.0, 0.0],    # support - green
        2: [0.0, 0.0, 1.0],    # pallet - blue
        3: [0.5, 0.5, 0.5]     # non-pile - gray
    }
    
    # Create combined point cloud with side-by-side layout
    offset = np.array([np.max(points[:, 0]) - np.min(points[:, 0]) + 2, 0, 0])
    
    # Ground truth on the left
    points_gt = points.copy()
    gt_colors = np.array([class_colors.get(label, [0, 0, 0]) for label in true_labels])
    
    # Predictions on the right
    points_pred = points + offset
    pred_colors = np.array([class_colors.get(label, [0, 0, 0]) for label in pred_labels])
    
    # Combine both point clouds
    combined_points = np.vstack([points_gt, points_pred])
    combined_colors = np.vstack([gt_colors, pred_colors])
    
    # Create combined point cloud
    pcd_combined = o3d.geometry.PointCloud()
    pcd_combined.points = o3d.utility.Vector3dVector(combined_points)
    pcd_combined.colors = o3d.utility.Vector3dVector(combined_colors)
    
    # Add text labels (using coordinate frames as markers)
    coord_gt = o3d.geometry.TriangleMesh.create_coordinate_frame(size=1.0)
    coord_gt.translate([np.min(points_gt[:, 0]), np.min(points_gt[:, 1]), np.max(points_gt[:, 2]) + 1])
    
    coord_pred = o3d.geometry.TriangleMesh.create_coordinate_frame(size=1.0)
    coord_pred.translate([np.min(points_pred[:, 0]), np.min(points_pred[:, 1]), np.max(points_pred[:, 2]) + 1])
    
    print(f"\nSide-by-side comparison for {cluster_id}:")
    print("Left: Ground Truth | Right: Predictions")
    
    o3d.visualization.draw_geometries([pcd_combined, coord_gt, coord_pred], 
                                    window_name=f"GT vs Predictions - {cluster_id}")
    
    return pcd_combined

def visualize_prediction_errors(points, true_labels, pred_labels, confidence_scores, 
                               class_map, cluster_id="error_analysis"):
    """Visualize prediction errors with different colors for different types of mistakes"""
    
    # Create error mask
    correct_mask = true_labels == pred_labels
    error_mask = ~correct_mask
    
    # Color scheme for errors
    error_colors = np.zeros((len(points), 3))
    
    # Correct predictions - green
    error_colors[correct_mask] = [0, 1, 0]
    
    # Different error types with different colors
    for i in range(len(points)):
        if error_mask[i]:
            true_label = true_labels[i]
            pred_label = pred_labels[i]
            confidence = confidence_scores[i, pred_label]
            
            # Color based on error type and confidence
            if true_label == 0 and pred_label != 0:  # Missed pile
                error_colors[i] = [1, 0, 0]  # Red
            elif true_label != 0 and pred_label == 0:  # False pile detection
                error_colors[i] = [1, 0.5, 0]  # Orange
            else:  # Other misclassifications
                error_colors[i] = [1, 1, 0]  # Yellow
            
            # Modulate intensity by confidence (low confidence = darker)
            error_colors[i] = error_colors[i] * (0.3 + 0.7 * confidence)
    
    # Create error visualization point cloud
    pcd_errors = o3d.geometry.PointCloud()
    pcd_errors.points = o3d.utility.Vector3dVector(points)
    pcd_errors.colors = o3d.utility.Vector3dVector(error_colors)
    
    # Calculate error statistics
    total_points = len(points)
    correct_points = np.sum(correct_mask)
    error_points = np.sum(error_mask)
    accuracy = correct_points / total_points
    
    # Pile-specific errors
    pile_true = true_labels == 0
    pile_pred = pred_labels == 0
    
    missed_piles = np.sum(pile_true & ~pile_pred)
    false_piles = np.sum(~pile_true & pile_pred)
    correct_piles = np.sum(pile_true & pile_pred)
    
    print(f"\nError Analysis for {cluster_id}:")
    print("-" * 40)
    print(f"Overall Accuracy: {accuracy:.3f} ({correct_points}/{total_points})")
    print(f"Total Errors: {error_points} points")
    print(f"\nPile Detection Errors:")
    print(f"  Missed Piles (Red): {missed_piles} points")
    print(f"  False Piles (Orange): {false_piles} points")
    print(f"  Correct Piles: {correct_piles} points")
    print(f"\nColor Legend:")
    print(f"  Green: Correct predictions")
    print(f"  Red: Missed pile points")
    print(f"  Orange: False pile detections")
    print(f"  Yellow: Other misclassifications")
    print(f"  Darker colors: Lower confidence")
    
    o3d.visualization.draw_geometries([pcd_errors], 
                                    window_name=f"Error Analysis - {cluster_id}")
    
    return pcd_errors, {
        'accuracy': accuracy,
        'total_errors': error_points,
        'missed_piles': missed_piles,
        'false_piles': false_piles,
        'correct_piles': correct_piles
    }

def segment_cluster_pointnet_plus(model, cluster_file, class_map, num_points=4096, device='cpu'):
    """Segment a single cluster with PointNet++"""
    model.eval()
    
    # Load point cloud
    pcd = o3d.io.read_point_cloud(cluster_file)
    original_points = np.asarray(pcd.points)
    
    print(f"Loaded cluster with {len(original_points)} points")
    
    # Process points with enhanced preprocessing
    dataset = PileClusterDataset(".", num_points=num_points)
    processed_points = dataset.process_points_enhanced(original_points)
    
    # Convert to tensor
    points_tensor = torch.FloatTensor(processed_points).transpose(1, 0).unsqueeze(0).to(device)
    
    with torch.no_grad():
        pred = model(points_tensor)  # [1, N, num_classes]
        pred = pred.squeeze(0)  # [N, num_classes]
        predicted_labels = torch.argmax(pred, dim=1).cpu().numpy()
        confidence_scores = torch.softmax(pred, dim=1).cpu().numpy()
    
    print(f"Segmentation complete. Processed {len(processed_points)} points")
    
    return original_points, processed_points, predicted_labels, confidence_scores

def demo_pointnet_plus_segmentation():
    """Demonstration of PointNet++ pile segmentation"""
    print("=== POINTNET++ PILE SEGMENTATION DEMO ===")
    print("Enhanced with hierarchical feature learning and better geometric understanding")
    
    # Check if we have data for training
    cluster_dir = Path("labeled_clusters")
    labels_dir = cluster_dir  # both clusters and labels are now in the same directory
    
    if not cluster_dir.exists():
        print(f"Cluster directory '{cluster_dir}' not found.")
        print("Please run DBSCAN clustering first to generate cluster files.")
        return
    
    cluster_files = list(cluster_dir.glob("*.ply"))
    if len(cluster_files) == 0:
        print("No cluster files found. Please ensure .ply files exist in the cluster directory.")
        return
    
    print(f"Found {len(cluster_files)} cluster files")
    
    # Check if we have labeled data for training
    if labels_dir.exists() and len(list(labels_dir.glob("*.npy"))) > 0:
        print("Found labeled data - training PointNet++ model...")
        model, class_map = train_pointnet_plus_segmentation()
        
        if model is None:
            print("Training failed. Please check your data.")
            return
            
    else:
        print("No labeled data found.")
        print("For demonstration, we'll create a model with default classes.")
        
        # Create model with default classes
        class_map = {'pile': 0, 'support': 1, 'pallet': 2, 'non-pile': 3}
        model = PointNetPlusPlusSegmentation(num_classes=len(class_map)).to(device)
        print("Created PointNet++ model for demonstration")
    
    # Demonstrate inference on a sample cluster
    sample_cluster = cluster_files[0]
    print(f"\nDemonstrating inference on: {sample_cluster.name}")
    
    try:
        original_points, processed_points, predicted_labels, confidence_scores = segment_cluster_pointnet_plus(
            model, str(sample_cluster), class_map, device=device
        )
        
        # Analyze results
        analysis = analyze_pile_structure(processed_points, predicted_labels, confidence_scores, class_map)
        
        print("\n=== ANALYSIS RESULTS ===")
        print(f"Total points processed: {analysis['total_points']}")
        
        print("\nClass distribution:")
        for class_name, data in analysis['class_distribution'].items():
            print(f"  {class_name}: {data['count']} points ({data['percentage']:.1f}%)")
        
        if analysis['pile_analysis']:
            pile_info = analysis['pile_analysis']
            print("\nPile analysis:")
            print(f"  Centroid: ({pile_info['centroid'][0]:.2f}, {pile_info['centroid'][1]:.2f}, {pile_info['centroid'][2]:.2f})")
            print(f"  Height: {pile_info['height']:.2f}m")
            print(f"  Estimated diameter: {pile_info['estimated_diameter']:.2f}m")
            print(f"  Average confidence: {pile_info['avg_confidence']:.3f}")
            print(f"  Quality score: {pile_info['quality_score']:.3f}")
        else:
            print("\nNo pile detected in this cluster")
        
        # Visualize results
        print("\nVisualizing segmentation results...")
        visualize_segmentation(processed_points, predicted_labels, confidence_scores, class_map, 
                             title=f"PointNet++ Segmentation - {sample_cluster.name}")
        
    except Exception as e:
        print(f"Error during inference: {str(e)}")
        print("This is expected if no trained model is available.")
    
    print("\n=== DEMO COMPLETE ===")
    print("Key features of PointNet++:")
    print("- Hierarchical feature learning")
    print("- Better handling of point density variations")
    print("- Improved geometric understanding")
    print("- Enhanced preprocessing with FPS sampling")
    print("- Comprehensive pile analysis and reporting")

def comprehensive_evaluation_demo():
    """Comprehensive evaluation with confusion matrix, IoU, and detailed visualizations"""
    print("=== COMPREHENSIVE POINTNET++ EVALUATION DEMO ===")
    
    # Check for data
    cluster_dir = Path("labeled_clusters")
    labels_dir = cluster_dir
    
    if not cluster_dir.exists() or len(list(cluster_dir.glob("*.ply"))) == 0:
        print("No cluster data found. Please run DBSCAN clustering first.")
        return
    
    if not labels_dir.exists() or len(list(labels_dir.glob("*.npy"))) == 0:
        print("No ground truth labels found. Creating synthetic data for demonstration...")
        
        # Create synthetic evaluation data
        cluster_files = list(cluster_dir.glob("*.ply"))[:3]  # Use first 3 clusters
        
        for cluster_file in cluster_files:
            # Load point cloud
            pcd = o3d.io.read_point_cloud(str(cluster_file))
            points = np.asarray(pcd.points)
            
            # Create synthetic labels (for demonstration)
            num_points = min(len(points), 4096)
            synthetic_labels = np.random.choice([0, 1, 2, 3], size=num_points, p=[0.4, 0.2, 0.2, 0.2])
            
            # Save synthetic labels
            labels_dir.mkdir(exist_ok=True)
            np.save(labels_dir / f"{cluster_file.stem}_labels.npy", synthetic_labels)
        
        print(f"Created synthetic labels for {len(cluster_files)} clusters")
    
    # Load or create model
    class_map = {'pile': 0, 'support': 1, 'pallet': 2, 'non-pile': 3}
    model = PointNetPlusPlusSegmentation(num_classes=len(class_map)).to(device)
    
    # Get clusters with labels
    labeled_clusters = []
    for label_file in labels_dir.glob("*.npy"):
        cluster_name = label_file.stem.replace("_labels", "")
        cluster_file = cluster_dir / f"{cluster_name}.ply"
        if cluster_file.exists():
            labeled_clusters.append((cluster_file, label_file))
    
    if not labeled_clusters:
        print("No matching cluster and label files found.")
        return
    
    print(f"Found {len(labeled_clusters)} clusters with labels")
    
    # Evaluate on all labeled clusters
    all_true_labels = []
    all_pred_labels = []
    all_confidences = []
    
    for i, (cluster_file, label_file) in enumerate(labeled_clusters[:3]):  # Limit to 3 for demo
        print(f"\nEvaluating cluster {i+1}/{min(3, len(labeled_clusters))}: {cluster_file.name}")
        
        try:
            # Load and process data
            original_points, processed_points, predicted_labels, confidence_scores = segment_cluster_pointnet_plus(
                model, str(cluster_file), class_map, device=device
            )
            
            # Load ground truth
            true_labels = np.load(label_file)
            
            # Ensure same length
            if len(true_labels) != len(processed_points):
                dataset = PileClusterDataset(".", num_points=len(processed_points))
                true_labels = dataset.process_labels(true_labels, len(processed_points))
            
            # Accumulate for overall evaluation
            all_true_labels.extend(true_labels)
            all_pred_labels.extend(predicted_labels)
            all_confidences.extend(confidence_scores)
            
            # Individual cluster evaluation
            print(f"\n--- Cluster {cluster_file.stem} Evaluation ---")
            class_names = list(class_map.keys())
            
            # Plot confusion matrix and IoU for this cluster
            iou_scores, mean_iou = plot_confusion_matrix_and_iou(
                true_labels, predicted_labels, class_names,
                title=f"Cluster {cluster_file.stem} Evaluation"
            )
            
            # Detailed visualizations
            print("\nShowing detailed visualizations (close windows to continue)...")
            
            # Side-by-side comparison
            create_side_by_side_comparison(
                processed_points, true_labels, predicted_labels, confidence_scores,
                class_map, cluster_id=cluster_file.stem
            )
            
            # Error analysis
            error_pcd, error_stats = visualize_prediction_errors(
                processed_points, true_labels, predicted_labels, confidence_scores,
                class_map, cluster_id=cluster_file.stem
            )
            
        except Exception as e:
            print(f"Error processing {cluster_file.name}: {str(e)}")
            continue
    
    # Overall evaluation across all clusters
    if all_true_labels and all_pred_labels:
        print("\n" + "="*60)
        print("OVERALL EVALUATION ACROSS ALL CLUSTERS")
        print("="*60)
        
        all_true_labels = np.array(all_true_labels)
        all_pred_labels = np.array(all_pred_labels)
        all_confidences = np.array(all_confidences)
        
        # Overall confusion matrix and IoU
        class_names = list(class_map.keys())
        overall_iou_scores, overall_mean_iou = plot_confusion_matrix_and_iou(
            all_true_labels, all_pred_labels, class_names,
            title="Overall Performance Across All Clusters"
        )
        
        # Overall statistics
        overall_accuracy = np.mean(all_true_labels == all_pred_labels)
        print(f"\nOverall Statistics:")
        print(f"  Total points evaluated: {len(all_true_labels)}")
        print(f"  Overall accuracy: {overall_accuracy:.3f}")
        print(f"  Overall mean IoU: {overall_mean_iou:.3f}")
        
        # Per-class statistics
        print(f"\nPer-class performance:")
        for class_name, scores in overall_iou_scores.items():
            class_accuracy = scores['tp'] / (scores['tp'] + scores['fn']) if (scores['tp'] + scores['fn']) > 0 else 0
            print(f"  {class_name}: IoU={scores['iou']:.3f}, Accuracy={class_accuracy:.3f}")
    
    print("\n=== EVALUATION DEMO COMPLETE ===")
    print("\nKey evaluation features demonstrated:")
    print("- Confusion matrix visualization")
    print("- Per-class IoU calculation")
    print("- Side-by-side GT vs Prediction comparison")
    print("- Error analysis with color-coded mistakes")
    print("- Overall performance across multiple clusters")

from pathlib import Path

cluster_dir = Path("labeled_clusters")
npy_files = list(cluster_dir.glob("*.npy"))
print(f"Found {len(npy_files)} .npy label files")


# Run the demonstration
demo_pointnet_plus_segmentation()

# Run the comprehensive evaluation demo
# This includes confusion matrix, IoU metrics, and detailed visualizations
comprehensive_evaluation_demo()

# Example: Evaluate a specific cluster if you have the data
# Uncomment and modify the paths below to use with your data

# cluster_file = "path/to/your/cluster.ply"
# label_file = "path/to/your/labels.npy"
# 
# if Path(cluster_file).exists() and Path(label_file).exists():
#     # Load data
#     pcd = o3d.io.read_point_cloud(cluster_file)
#     points = np.asarray(pcd.points)
#     true_labels = np.load(label_file)
#     
#     # Create model and get predictions
#     class_map = {'pile': 0, 'support': 1, 'pallet': 2, 'non-pile': 3}
#     model = PointNetPlusPlusSegmentation(num_classes=len(class_map)).to(device)
#     
#     # Get predictions
#     original_points, processed_points, predicted_labels, confidence_scores = segment_cluster_pointnet_plus(
#         model, cluster_file, class_map, device=device
#     )
#     
#     # Ensure same length
#     if len(true_labels) != len(processed_points):
#         dataset = PileClusterDataset(".", num_points=len(processed_points))
#         true_labels = dataset.process_labels(true_labels, len(processed_points))
#     
#     # Run evaluations
#     class_names = list(class_map.keys())
#     
#     # Confusion matrix and IoU
#     iou_scores, mean_iou = plot_confusion_matrix_and_iou(
#         true_labels, predicted_labels, class_names, title="Custom Evaluation"
#     )
#     
#     # Detailed visualizations
#     visualize_predictions_detailed(
#         processed_points, true_labels, predicted_labels, confidence_scores,
#         class_map, cluster_id="custom"
#     )
#     
#     # Error analysis
#     error_pcd, error_stats = visualize_prediction_errors(
#         processed_points, true_labels, predicted_labels, confidence_scores,
#         class_map, cluster_id="custom"
#     )

print("Custom evaluation template ready - uncomment and modify paths to use with your data")