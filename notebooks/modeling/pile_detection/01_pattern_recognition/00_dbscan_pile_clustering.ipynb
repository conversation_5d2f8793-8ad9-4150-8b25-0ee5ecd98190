!ls -lh ../../../../data/raw/piani_di_giorgio/pointcloud/

# Parameters
#POINT_CLOUD_PATH = "../../../../data/processed/trino_enel/ground_segmentation/ransac_pmf/trino_enel_nonground.ply"

#POINT_CLOUD_PATH = "../../../../data/raw/piani_di_giorgio/pointcloud/Croped_pointcloud_FLy4_Giorgio.las"

POINT_CLOUD_PATH = "../../../../data/raw/nortan_res/pointcloud/Block_11_2m.las"
OUTPUT_DIR = "dbscan_clusters"

# DBSCAN parameters
DBSCAN_EPS = 2.5  # max distance (meters) between points in a cluster
DBSCAN_MIN_SAMPLES = 8  # minimum points to form a cluster

# Output options
SAVE_INDIVIDUAL_CLUSTERS = True
SAVE_CENTROIDS = True
VISUALIZE_RESULTS = True


print("=== PILE DISCOVERY PIPELINE (NO METADATA) ===")
print(f"Target: Discover all pile structures on site")
print(f"DBSCAN eps: {DBSCAN_EPS}m, min_samples: {DBSCAN_MIN_SAMPLES}")
print(f"Output directory: {OUTPUT_DIR}")

import open3d as o3d
import numpy as np
import pandas as pd
from pathlib import Path
import os
from datetime import datetime
import json
import matplotlib.pyplot as plt
from collections import defaultdict
from sklearn.cluster import DBSCAN
import warnings
warnings.filterwarnings('ignore')


!ls -lh ../../../../data/processed/trino_enel

import os
import laspy
import numpy as np
import open3d as o3d

def load_point_cloud(file_path):
    ext = os.path.splitext(file_path)[1].lower()
    
    if ext == '.las':
        las = laspy.read(file_path)
        points = np.vstack((las.x, las.y, las.z)).T
        pcd = o3d.geometry.PointCloud()
        pcd.points = o3d.utility.Vector3dVector(points)
    elif ext == '.ply':
        pcd = o3d.io.read_point_cloud(file_path)
    else:
        raise ValueError(f"Unsupported file extension: {ext}")
    
    return pcd

pcd = load_point_cloud(POINT_CLOUD_PATH)
o3d.visualization.draw_geometries([pcd])


pcd, ind = pcd.remove_statistical_outlier(nb_neighbors=20, std_ratio=2.0)
points = np.asarray(pcd.points)

import numpy as np
from sklearn.cluster import DBSCAN
import open3d as o3d

# Downsample the cloud
pcd_down = pcd.voxel_down_sample(voxel_size=0.5)
points = np.asarray(pcd_down.points)

print(f"Running DBSCAN on {points.shape[0]} points")

# Clustering
eps = 2.5
min_samples = 10
clustering = DBSCAN(eps=eps, min_samples=min_samples).fit(points)
labels = clustering.labels_
n_clusters = len(set(labels)) - (1 if -1 in labels else 0)

print(f"Found {n_clusters} clusters")


import matplotlib.pyplot as plt
import open3d as o3d
import numpy as np

def visualize_clusters(points, labels):
    # Create Open3D point cloud
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(points)

    # Colormap: RGB only, skip alpha
    cmap = plt.get_cmap("tab20")
    colors = [
        cmap(label % 20)[:3] if label != -1 else (0, 0, 0)  # strip alpha
        for label in labels
    ]

    pcd.colors = o3d.utility.Vector3dVector(colors)
    o3d.visualization.draw_geometries([pcd])


from collections import Counter

def filter_top_clusters(points, labels, top_k=200):
    cluster_sizes = Counter(labels)
    top_labels = set(
        label for label, count in cluster_sizes.most_common(top_k) if label != -1
    )
    mask = np.isin(labels, list(top_labels))
    return points[mask], labels[mask]

filtered_points, filtered_labels = filter_top_clusters(points, labels, top_k=200)

visualize_clusters(filtered_points, filtered_labels)


from collections import defaultdict

clusters = defaultdict(list)

# Group points by cluster label
for point, label in zip(points, labels):
    if label == -1:
        continue
    clusters[label].append(point)

centroids = []
for cluster_id, pts in clusters.items():
    pts = np.array(pts)
    center = pts.mean(axis=0)
    centroids.append(center)

centroids = np.array(centroids)
print(f"Extracted {len(centroids)} pile centroids")


import os

os.makedirs("dbscan_clusters", exist_ok=True)

# Save each cluster as separate PCD/PLY
for cluster_id, pts in clusters.items():
    cluster_pcd = o3d.geometry.PointCloud()
    cluster_pcd.points = o3d.utility.Vector3dVector(np.array(pts))
    o3d.io.write_point_cloud(f"dbscan_clusters/cluster_{cluster_id:03d}.ply", cluster_pcd)

# Save centroids
np.savetxt("pile_centroids.csv", centroids, delimiter=",", header="x,y,z", comments="")
print("Cluster point clouds and centroids saved.")


import os
import numpy as np
import pandas as pd
import open3d as o3d
import laspy
from pathlib import Path
from sklearn.cluster import DBSCAN
from collections import defaultdict, Counter
import matplotlib.pyplot as plt

# === CONFIGURATION ===
POINT_CLOUD_PATH = "../../../../data/raw/nortan_res/pointcloud/Block_11_2m.las"
OUTPUT_DIR = "dbscan_clusters"
LABELED_DIR = "labeled_clusters"

DBSCAN_EPS = 2.5
DBSCAN_MIN_SAMPLES = 8
VOXEL_DOWNSAMPLE = 0.5

# === STEP 1: LOAD POINT CLOUD ===
def load_point_cloud(file_path):
    ext = os.path.splitext(file_path)[1].lower()
    if ext == '.las':
        las = laspy.read(file_path)
        points = np.vstack((las.x, las.y, las.z)).T
        pcd = o3d.geometry.PointCloud()
        pcd.points = o3d.utility.Vector3dVector(points)
    elif ext == '.ply':
        pcd = o3d.io.read_point_cloud(file_path)
    else:
        raise ValueError(f"Unsupported file extension: {ext}")
    return pcd

print("Loading point cloud...")
pcd = load_point_cloud(POINT_CLOUD_PATH)
pcd, _ = pcd.remove_statistical_outlier(nb_neighbors=20, std_ratio=2.0)
pcd = pcd.voxel_down_sample(voxel_size=VOXEL_DOWNSAMPLE)
points = np.asarray(pcd.points)

# === STEP 2: CLUSTERING ===
print("Running DBSCAN clustering...")
clustering = DBSCAN(eps=DBSCAN_EPS, min_samples=DBSCAN_MIN_SAMPLES).fit(points)
labels = clustering.labels_
n_clusters = len(set(labels)) - (1 if -1 in labels else 0)
print(f"Found {n_clusters} clusters")

# === STEP 3: GROUP POINTS BY CLUSTER ===
clusters = defaultdict(list)
for point, label in zip(points, labels):
    if label != -1:
        clusters[label].append(point)

# === STEP 4: AUTO-LABEL POINTS IN EACH CLUSTER ===
def label_cluster_points(points, center, height_thresh=0.3, radius_thresh=0.6):
    points = np.array(points)
    dists = np.linalg.norm(points[:, :2] - center[:2], axis=1)
    heights = points[:, 2]
    z_ground = np.percentile(heights, 5)

    labels = []
    for i in range(len(points)):
        dist_ok = dists[i] < radius_thresh
        height_ok = heights[i] > (z_ground + height_thresh)
        if dist_ok and height_ok:
            labels.append(1)  # pile
        elif heights[i] < (z_ground + 0.1):
            labels.append(0)  # ground
        else:
            labels.append(2)  # noise
    return labels

os.makedirs(OUTPUT_DIR, exist_ok=True)
os.makedirs(LABELED_DIR, exist_ok=True)
centroids = []
all_metadata = []

print("Saving clusters and labeled outputs...")
for cluster_id, pts in clusters.items():
    pts = np.array(pts)
    center = pts.mean(axis=0)
    centroids.append(center)
    labels = label_cluster_points(pts, center)

    # Color coding
    label_colors = {
        0: [0.4, 0.4, 0.4],  # ground = gray
        1: [0, 1, 0],        # pile = green
        2: [1, 0, 0],        # noise = red
    }
    colors = np.array([label_colors[l] for l in labels])

    # Save colored PCD
    cluster_pcd = o3d.geometry.PointCloud()
    cluster_pcd.points = o3d.utility.Vector3dVector(pts)
    cluster_pcd.colors = o3d.utility.Vector3dVector(colors)
    o3d.io.write_point_cloud(f"{LABELED_DIR}/cluster_{cluster_id:03d}.ply", cluster_pcd)

    # Save labeled .npz for ML
    np.savez(f"{LABELED_DIR}/cluster_{cluster_id:03d}.npz", points=pts, labels=np.array(labels))

    all_metadata.append({
        "cluster_id": cluster_id,
        "num_points": len(pts),
        "num_pile": int(np.sum(np.array(labels) == 1)),
        "num_ground": int(np.sum(np.array(labels) == 0)),
        "num_noise": int(np.sum(np.array(labels) == 2))
    })

# Save centroids and metadata
np.savetxt("pile_centroids.csv", np.array(centroids), delimiter=",", header="x,y,z", comments="")
pd.DataFrame(all_metadata).to_csv(f"{LABELED_DIR}/label_summary.csv", index=False)
print("All outputs saved.")


import numpy as np
import open3d as o3d
from sklearn.cluster import DBSCAN
import os
import json
from collections import Counter

def load_point_cloud(path):
    pcd = o3d.io.read_point_cloud(path)
    return np.asarray(pcd.points)

def cluster_dbscan(points, eps=0.5, min_samples=10):
    clustering = DBSCAN(eps=eps, min_samples=min_samples).fit(points[:, :3])
    labels = clustering.labels_
    return labels

def extract_clusters(points, labels):
    clusters = {}
    for i, label in enumerate(labels):
        if label == -1:
            continue
        clusters.setdefault(label, []).append(points[i])
    return clusters

def get_bounding_box_stats(cluster_points):
    cluster_points = np.array(cluster_points)
    min_bounds = np.min(cluster_points, axis=0)
    max_bounds = np.max(cluster_points, axis=0)
    dims = max_bounds - min_bounds
    footprint = dims[0] * dims[1]
    height = dims[2]
    return dims, footprint, height

def classify_cluster(cluster_points, height_thresh=0.3):
    cluster_points = np.array(cluster_points)
    dims, footprint, height = get_bounding_box_stats(cluster_points)

    # Heuristic rules
    if height > 0.6 and footprint < 1.0 and 0.4 < dims[0] < 1.2 and 0.4 < dims[1] < 1.2:
        return "pile"
    elif height < 0.4 and footprint > 1.5:
        return "pallet"
    elif footprint > 0.8 and dims[0] / dims[1] > 2.0:
        return "machinery"
    else:
        return "noise"

def save_cluster_ply(cluster_points, label, cluster_id, out_dir):
    cluster_pcd = o3d.geometry.PointCloud()
    cluster_pcd.points = o3d.utility.Vector3dVector(np.array(cluster_points))
    filename = os.path.join(out_dir, f"{label}_cluster_{cluster_id:03d}.ply")
    o3d.io.write_point_cloud(filename, cluster_pcd)

def run_pipeline(pcd_path, output_dir="labeled_clusters", eps=0.5, min_samples=10, save=True):
    os.makedirs(output_dir, exist_ok=True)

    print("Loading point cloud...")
    points = load_point_cloud(pcd_path)
    print(f"Loaded {len(points)} points")

    print("Clustering...")
    labels = cluster_dbscan(points, eps=eps, min_samples=min_samples)
    clusters = extract_clusters(points, labels)
    print(f"Found {len(clusters)} clusters")

    label_summary = {}

    for cid, cluster_points in clusters.items():
        label = classify_cluster(cluster_points)
        label_summary[cid] = label
        print(f"Cluster {cid}: {label} ({len(cluster_points)} pts)")
        if save:
            save_cluster_ply(cluster_points, label, cid, output_dir)

    with open(os.path.join(output_dir, "cluster_labels.json"), "w") as f:
        json.dump(label_summary, f, indent=2)

    print("Done.")
    print("Label counts:", dict(Counter(label_summary.values())))

# Example usage
if __name__ == "__main__":
    POINT_CLOUD_PATH = "../../../../data/raw/nortan_res/pointcloud/Block_11_2m.las"
    OUTPUT_DIR = "dbscan_clusters"
    LABELED_DIR = "labeled_clusters"

    run_pipeline(POINT_CLOUD_PATH, LABELED_DIR)


import os
import numpy as np
import open3d as o3d
from sklearn.cluster import DBSCAN
import matplotlib.pyplot as plt
import laspy
import json

def load_point_cloud_las(file_path, voxel_size=0.3):
    print(f"Reading LAS file: {file_path}")
    las = laspy.read(file_path)
    coords = np.vstack((las.x, las.y, las.z)).T

    # Downsample using voxel grid
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(coords)

    if voxel_size:
        pcd = pcd.voxel_down_sample(voxel_size=voxel_size)
        coords = np.asarray(pcd.points)

    print(f"Loaded and downsampled to {len(coords)} points")
    return coords, pcd

def cluster_dbscan(points, eps=0.5, min_samples=10):
    return DBSCAN(eps=eps, min_samples=min_samples).fit(points).labels_

def extract_clusters(points, labels):
    clusters = {}
    for idx in np.unique(labels):
        if idx == -1:
            continue  # noise
        clusters[idx] = points[labels == idx]
    return clusters

def heuristic_label_cluster(cluster):
    x_range = np.ptp(cluster[:, 0])
    y_range = np.ptp(cluster[:, 1])
    z_range = np.ptp(cluster[:, 2])

    if z_range > x_range and z_range > y_range:
        return "pile"
    elif x_range > 1.5 and y_range > 1.5 and z_range < 0.8:
        return "pallet"
    elif x_range > 1.0 and z_range > 1.5:
        return "machinery"
    else:
        return "noise"

def visualize_labeled_clusters(clusters, labels_map):
    colors = {
        "pile": [1, 0, 0],
        "pallet": [0, 1, 0],
        "machinery": [0, 0, 1],
        "noise": [0.5, 0.5, 0.5]
    }
    all_points = []
    all_colors = []
    for cid, pts in clusters.items():
        label = labels_map[cid]
        color = np.tile(colors[label], (pts.shape[0], 1))
        all_points.append(pts)
        all_colors.append(color)

    merged_pcd = o3d.geometry.PointCloud()
    merged_pcd.points = o3d.utility.Vector3dVector(np.vstack(all_points))
    merged_pcd.colors = o3d.utility.Vector3dVector(np.vstack(all_colors))
    o3d.visualization.draw_geometries([merged_pcd])

def save_labeled_clusters(clusters, labels_map, save_dir="labeled_clusters"):
    os.makedirs(save_dir, exist_ok=True)

    label_summary = {}
    for cluster_id, points in clusters.items():
        label = labels_map.get(cluster_id, "unknown")
        cluster_name = f"cluster_{cluster_id:03d}_{label}"
        
        # Save cluster as PLY
        ply_path = os.path.join(save_dir, f"{cluster_name}.ply")
        pcd = o3d.geometry.PointCloud()
        pcd.points = o3d.utility.Vector3dVector(points)
        o3d.io.write_point_cloud(ply_path, pcd)

        # Save label as a .npy file (all points in cluster get the same class)
        npy_path = os.path.join(save_dir, f"{cluster_name}.npy")
        class_id = {
            "pile": 0,
            "support": 1,
            "pallet": 2,
            "machinery": 3,
            "noise": 4
        }.get(label, 4)  # default to 'noise' if unknown

        labels_array = np.full((points.shape[0],), class_id, dtype=np.int64)
        np.save(npy_path, labels_array)

        # Track summary
        if label not in label_summary:
            label_summary[label] = []
        label_summary[label].append(cluster_name)

    # Save summary to JSON
    summary_path = os.path.join(save_dir, "label_summary.json")
    with open(summary_path, "w") as f:
        json.dump(label_summary, f, indent=4)

    print(f"\nSaved {len(clusters)} clusters with labels to: {save_dir}")
    print(f"Label distribution: { {k: len(v) for k, v in label_summary.items()} }")

def run_pipeline(pcd_path):
    points, _ = load_point_cloud_las(pcd_path, voxel_size=0.3)  # Tune voxel_size
    print(f"Processing {len(points)} points")

    labels = cluster_dbscan(points, eps=0.8, min_samples=20)
    clusters = extract_clusters(points, labels)
    print(f"Found {len(clusters)} clusters")

    # Apply heuristic labeling per cluster
    labels_map = {}
    for cid, cluster_points in clusters.items():
        label = heuristic_label_cluster(cluster_points)
        labels_map[cid] = label


    save_labeled_clusters(clusters, labels_map, LABELED_DIR)
    return clusters, labels_map
# Example usage:
POINT_CLOUD_PATH = "../../../../data/raw/nortan_res/pointcloud/Block_11_2m.las"
OUTPUT_DIR = "dbscan_clusters"
LABELED_DIR = "labeled_clusters"
clusters, labels_map = run_pipeline(POINT_CLOUD_PATH)



import matplotlib.pyplot as plt
import open3d as o3d
import random

def visualize_clusters_by_label(clusters, labels_map, label, num_samples=5):
    matching = [cid for cid, lbl in labels_map.items() if lbl == label]
    sample_ids = random.sample(matching, min(len(matching), num_samples))

    for cid in sample_ids:
        pcd = o3d.geometry.PointCloud()
        pcd.points = o3d.utility.Vector3dVector(clusters[cid])
        o3d.visualization.draw_geometries([pcd], window_name=f"{label} - Cluster {cid}")

# Visualize samples
#visualize_clusters_by_label(clusters, labels_map, "pallet", num_samples=5)
#visualize_clusters_by_label(clusters, labels_map, "machinery", num_samples=3)
visualize_clusters_by_label(clusters, labels_map, "pile", num_samples=5)

def visualize_clusters_by_label(clusters, labels_map, save_path=None):
    import open3d as o3d
    import random

    label_colors = {
        "pallet": [1.0, 0.0, 0.0],      # Red
        "machinery": [0.0, 1.0, 0.0],   # Green
        "noise": [0.5, 0.5, 0.5],       # Gray
    }

    vis_list = []
    for cluster_id, pts in clusters.items():
        label = labels_map.get(cluster_id, "noise")
        color = label_colors.get(label, [random.random(), random.random(), random.random()])
        pcd = o3d.geometry.PointCloud()
        pcd.points = o3d.utility.Vector3dVector(pts)
        pcd.paint_uniform_color(color)
        vis_list.append(pcd)

    if save_path:
        vis = o3d.visualization.Visualizer()
        vis.create_window(visible=False)
        for pcd in vis_list:
            vis.add_geometry(pcd)
        vis.poll_events()
        vis.update_renderer()
        vis.capture_screen_image(save_path)
        vis.destroy_window()
        print(f"Saved visualization screenshot to {save_path}")
    else:
        o3d.visualization.draw_geometries(vis_list)

visualize_clusters_by_label(clusters, labels_map, "cluster_visualization.png")